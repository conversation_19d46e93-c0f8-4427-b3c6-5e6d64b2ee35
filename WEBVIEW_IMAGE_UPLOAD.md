# WebView 图片上传功能

## 概述

Seeq应用现已支持在WebView中上传图片，包括从图库选择和相机拍照两种方式。

## 功能特性

### 🖼️ 图片选择方式
- **图库选择**：支持单选和多选图片
- **相机拍照**：直接使用相机拍照上传
- **统一界面**：通过系统选择器提供统一的用户体验

### 🔐 权限管理
- **动态权限请求**：根据Android版本自动请求合适的权限
- **Android 13+ 支持**：使用新的媒体权限 `READ_MEDIA_IMAGES`
- **向下兼容**：支持Android 12及以下的传统存储权限

### 📁 文件安全
- **FileProvider**：使用安全的文件共享机制
- **临时文件**：相机拍照使用临时文件存储
- **权限控制**：严格的文件访问权限控制

## 权限列表

### 必需权限
```xml
<!-- 相机权限 -->
<uses-permission android:name="android.permission.CAMERA" />

<!-- Android 13+ 图片权限 -->
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

<!-- Android 12及以下存储权限 -->
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### FileProvider配置
```xml
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="${applicationId}.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
</provider>
```

## 技术实现

### 核心组件

1. **WebChromeClient.onShowFileChooser()**
   - 处理WebView的文件选择请求
   - 检查权限并启动文件选择器

2. **ActivityResultLauncher**
   - 现代化的Activity结果处理
   - 替代已废弃的startActivityForResult

3. **权限管理系统**
   - 动态权限请求
   - 版本兼容性处理

### 工作流程

```
WebView文件上传请求
        ↓
检查相机和存储权限
        ↓
权限不足 → 请求权限 → 权限被拒绝 → 取消上传
        ↓
权限充足 → 显示选择器
        ↓
用户选择图库/相机
        ↓
处理选择结果
        ↓
返回URI给WebView
```

## 支持的文件类型

- **图片格式**：JPEG, PNG, GIF, WebP
- **多选支持**：支持同时选择多张图片
- **大小限制**：由WebView和服务器端控制

## 错误处理

### 权限被拒绝
- 显示友好的提示消息
- 取消文件上传操作
- 记录详细的日志信息

### 文件创建失败
- 自动回退到图库选择
- 显示错误提示
- 确保不会崩溃

### 选择器启动失败
- 显示错误消息
- 清理回调状态
- 记录异常信息

## 测试建议

### 功能测试
1. 测试图库单选图片
2. 测试图库多选图片
3. 测试相机拍照
4. 测试权限请求流程
5. 测试权限被拒绝的情况

### 兼容性测试
1. Android 13+ 设备测试
2. Android 12及以下设备测试
3. 不同厂商ROM测试
4. 不同WebView版本测试

### 边界测试
1. 大文件上传测试
2. 网络异常情况测试
3. 存储空间不足测试
4. 相机不可用测试

## 日志记录

应用会记录详细的日志信息，包括：
- 权限请求和授予状态
- 文件选择器启动状态
- 选择的文件URI信息
- 错误和异常信息

查看日志：
```bash
adb logcat -s MainActivity
```

## 注意事项

1. **权限时机**：权限在用户触发文件上传时动态请求
2. **文件清理**：临时文件会在应用重启时自动清理
3. **内存管理**：大图片可能需要压缩处理
4. **网络上传**：实际上传由WebView和服务器处理
