1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="cn.ykload.seeq"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="36" />
10
11    <!-- 无障碍服务权限 -->
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:6:5-85
12-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:6:22-82
13    <!-- 查询已安装应用权限 -->
14    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
14-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- 网络权限 -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:11:22-76
18
19    <permission
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
20        android:name="cn.ykload.seeq.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="cn.ykload.seeq.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
24
25    <application
25-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:13:5-46:19
26        android:allowBackup="true"
26-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:15:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:16:9-54
32        android:icon="@mipmap/ic_launcher"
32-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:17:9-43
33        android:label="@string/app_name"
33-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:18:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:19:9-54
35        android:supportsRtl="true"
35-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:20:9-35
36        android:testOnly="true"
37        android:theme="@style/Theme.Seeq" >
37-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:21:9-42
38        <activity
38-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:22:9-32:20
39            android:name="cn.ykload.seeq.MainActivity"
39-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:23:13-41
40            android:exported="true"
40-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:24:13-36
41            android:label="@string/app_name"
41-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:25:13-45
42            android:theme="@style/Theme.AppCompat.Light.NoActionBar" >
42-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:26:13-69
43            <intent-filter>
43-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:27:13-31:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:28:17-69
44-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:28:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:30:17-77
46-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:30:27-74
47            </intent-filter>
48        </activity>
49
50        <!-- 无障碍服务 -->
51        <service
51-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:35:9-45:19
52            android:name="cn.ykload.seeq.SeqAccessibilityService"
52-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:36:13-52
53            android:exported="false"
53-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:37:13-37
54            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
54-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:38:13-79
55            <intent-filter>
55-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:39:13-41:29
56                <action android:name="android.accessibilityservice.AccessibilityService" />
56-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:40:17-92
56-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:40:25-89
57            </intent-filter>
58
59            <meta-data
59-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:42:13-44:72
60                android:name="android.accessibilityservice"
60-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:43:17-60
61                android:resource="@xml/accessibility_service_config" />
61-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:44:17-69
62        </service>
63
64        <activity
64-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
65            android:name="androidx.compose.ui.tooling.PreviewActivity"
65-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
66            android:exported="true" />
66-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
67        <activity
67-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
68            android:name="androidx.activity.ComponentActivity"
68-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
69            android:exported="true" />
69-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
70
71        <provider
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
72            android:name="androidx.startup.InitializationProvider"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
73            android:authorities="cn.ykload.seeq.androidx-startup"
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
74            android:exported="false" >
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
75            <meta-data
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
76                android:name="androidx.emoji2.text.EmojiCompatInitializer"
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
77                android:value="androidx.startup" />
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
78            <meta-data
78-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
79                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
79-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
80                android:value="androidx.startup" />
80-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
81            <meta-data
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
82                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
83                android:value="androidx.startup" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
84        </provider>
85
86        <receiver
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
87            android:name="androidx.profileinstaller.ProfileInstallReceiver"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
88            android:directBootAware="false"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
89            android:enabled="true"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
90            android:exported="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
91            android:permission="android.permission.DUMP" >
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
93                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
96                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
99                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
102                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
103            </intent-filter>
104        </receiver>
105    </application>
106
107</manifest>
