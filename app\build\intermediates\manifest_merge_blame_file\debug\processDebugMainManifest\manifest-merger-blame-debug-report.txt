1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="cn.ykload.seeq"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="31"
9        android:targetSdkVersion="36" />
10
11    <!-- 无障碍服务权限 -->
12    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
12-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:6:5-85
12-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:6:22-82
13    <!-- 查询已安装应用权限 -->
14    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
14-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:8:22-74
15    <!-- 网络权限 -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:11:5-79
17-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:11:22-76
18    <!-- 文件和相机权限 -->
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:13:5-80
19-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:13:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:14:22-78
21    <uses-permission android:name="android.permission.CAMERA" />
21-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:15:5-65
21-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:15:22-62
22    <!-- Android 13+ 图片权限 -->
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:17:5-76
23-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:17:22-73
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="cn.ykload.seeq.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="cn.ykload.seeq.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30
31    <application
31-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:19:5-63:19
32        android:allowBackup="true"
32-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:20:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aae282b3edfeee1699c61b5e94e80ed2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
34        android:dataExtractionRules="@xml/data_extraction_rules"
34-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:21:9-65
35        android:debuggable="true"
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:22:9-54
38        android:icon="@mipmap/ic_launcher"
38-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:23:9-43
39        android:label="@string/app_name"
39-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:24:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:25:9-54
41        android:supportsRtl="true"
41-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:26:9-35
42        android:testOnly="true"
43        android:theme="@style/Theme.Seeq" >
43-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:27:9-42
44        <activity
44-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:28:9-38:20
45            android:name="cn.ykload.seeq.MainActivity"
45-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:29:13-41
46            android:exported="true"
46-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:30:13-36
47            android:label="@string/app_name"
47-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:31:13-45
48            android:theme="@style/Theme.AppCompat.Light.NoActionBar" >
48-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:32:13-69
49            <intent-filter>
49-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:33:13-37:29
50                <action android:name="android.intent.action.MAIN" />
50-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:34:17-69
50-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:34:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:36:17-77
52-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:36:27-74
53            </intent-filter>
54        </activity>
55
56        <!-- 无障碍服务 -->
57        <service
57-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:41:9-51:19
58            android:name="cn.ykload.seeq.SeqAccessibilityService"
58-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:42:13-52
59            android:exported="false"
59-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:43:13-37
60            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
60-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:44:13-79
61            <intent-filter>
61-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:45:13-47:29
62                <action android:name="android.accessibilityservice.AccessibilityService" />
62-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:46:17-92
62-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:46:25-89
63            </intent-filter>
64
65            <meta-data
65-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:48:13-50:72
66                android:name="android.accessibilityservice"
66-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:49:17-60
67                android:resource="@xml/accessibility_service_config" />
67-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:50:17-69
68        </service>
69
70        <!-- FileProvider for sharing files -->
71        <provider
72            android:name="androidx.core.content.FileProvider"
72-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:55:13-62
73            android:authorities="cn.ykload.seeq.fileprovider"
73-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:56:13-64
74            android:exported="false"
74-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:57:13-37
75            android:grantUriPermissions="true" >
75-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:58:13-47
76            <meta-data
76-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:59:13-61:54
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:60:17-67
78                android:resource="@xml/file_paths" />
78-->D:\Projects\Seeq\app\src\main\AndroidManifest.xml:61:17-51
79        </provider>
80
81        <activity
81-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
82            android:name="androidx.compose.ui.tooling.PreviewActivity"
82-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
83            android:exported="true" />
83-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76ba9941e8d2095ab01d0b2f0001b415\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
84        <activity
84-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
85            android:name="androidx.activity.ComponentActivity"
85-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
86            android:exported="true" />
86-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92b36f4a90fa98e939f9a7f57dc3f2cf\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
87
88        <provider
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
90            android:authorities="cn.ykload.seeq.androidx-startup"
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
91            android:exported="false" >
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
92            <meta-data
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.emoji2.text.EmojiCompatInitializer"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
94                android:value="androidx.startup" />
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ae139cc0e8023047d35732112035efe\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
96-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
97                android:value="androidx.startup" />
97-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\c90929252726c44ac38ff13aedfd2d33\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
100                android:value="androidx.startup" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
101        </provider>
102
103        <receiver
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
104            android:name="androidx.profileinstaller.ProfileInstallReceiver"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
105            android:directBootAware="false"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
106            android:enabled="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
107            android:exported="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
108            android:permission="android.permission.DUMP" >
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
110                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
113                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
116                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
119                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dec8eb413bb9593f8aa31b74a1f62fa8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
120            </intent-filter>
121        </receiver>
122    </application>
123
124</manifest>
