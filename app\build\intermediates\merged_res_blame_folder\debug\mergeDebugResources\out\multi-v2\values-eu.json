{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bf986b2bb756bc676c370bdeef3914\\transformed\\material-1.10.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1137,1236,1304,1365,1453,1516,1582,1646,1717,1780,1834,1943,2002,2065,2119,2193,2318,2408,2488,2633,2716,2798,2936,3027,3110,3162,3215,3281,3352,3432,3518,3598,3676,3754,3827,3902,4009,4096,4183,4274,4367,4439,4515,4607,4658,4740,4806,4890,4976,5038,5102,5165,5233,5340,5449,5545,5650,5706,5763", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "270,377,482,562,669,769,867,982,1065,1132,1231,1299,1360,1448,1511,1577,1641,1712,1775,1829,1938,1997,2060,2114,2188,2313,2403,2483,2628,2711,2793,2931,3022,3105,3157,3210,3276,3347,3427,3513,3593,3671,3749,3822,3897,4004,4091,4178,4269,4362,4434,4510,4602,4653,4735,4801,4885,4971,5033,5097,5160,5228,5335,5444,5540,5645,5701,5758,5841"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,5065,5132,5231,5462,11970,12058,12121,12187,12251,12322,12385,12439,12548,12607,12670,12724,12798,12923,13013,13093,13238,13321,13403,13541,13632,13715,13767,13820,13886,13957,14037,14123,14203,14281,14359,14432,14507,14614,14701,14788,14879,14972,15044,15120,15212,15263,15345,15411,15495,15581,15643,15707,15770,15838,15945,16054,16150,16255,16311,16541", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,5127,5226,5294,5518,12053,12116,12182,12246,12317,12380,12434,12543,12602,12665,12719,12793,12918,13008,13088,13233,13316,13398,13536,13627,13710,13762,13815,13881,13952,14032,14118,14198,14276,14354,14427,14502,14609,14696,14783,14874,14967,15039,15115,15207,15258,15340,15406,15490,15576,15638,15702,15765,15833,15940,16049,16145,16250,16306,16363,16619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae282b3edfeee1699c61b5e94e80ed2\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,17196", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,17292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\282188e067ace35e7ceeb03ac6735130\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,998,1088,1162,1239,1321,1399,1476,1544", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,993,1083,1157,1234,1316,1394,1471,1539,1659"}, "to": {"startLines": "48,49,50,51,52,56,57,171,172,174,175,177,178,179,180,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4596,4687,4768,4869,4973,5299,5375,16368,16457,16624,16712,16885,16959,17036,17118,17297,17374,17442", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "4682,4763,4864,4968,5060,5370,5457,16452,16536,16707,16797,16954,17031,17113,17191,17369,17437,17557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,16802", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,16880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74b732bc14271422d5c17044383b41bc\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "185,186", "startColumns": "4,4", "startOffsets": "17562,17648", "endColumns": "85,88", "endOffsets": "17643,17732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5523,5670,5817,5944,6089,6218,6316,6431,6571,6690,6835,6919,7024,7120,7220,7339,7460,7570,7713,7857,7992,8183,8308,8430,8554,8676,8773,8870,8998,9133,9231,9334,9440,9587,9738,9846,9946,10022,10118,10213,10332,10419,10507,10617,10697,10782,10877,10980,11071,11170,11259,11367,11467,11573,11691,11771,11875", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "5665,5812,5939,6084,6213,6311,6426,6566,6685,6830,6914,7019,7115,7215,7334,7455,7565,7708,7852,7987,8178,8303,8425,8549,8671,8768,8865,8993,9128,9226,9329,9435,9582,9733,9841,9941,10017,10113,10208,10327,10414,10502,10612,10692,10777,10872,10975,11066,11165,11254,11362,11462,11568,11686,11766,11870,11965"}}]}]}