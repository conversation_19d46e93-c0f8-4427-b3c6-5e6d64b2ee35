{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\282188e067ace35e7ceeb03ac6735130\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1021,1112,1184,1262,1340,1415,1494,1564", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,1016,1107,1179,1257,1335,1410,1489,1559,1680"}, "to": {"startLines": "48,49,50,51,52,56,57,171,172,174,175,177,178,179,180,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4485,4581,4667,4766,4869,5192,5272,16106,16196,16363,16452,16625,16697,16775,16853,17029,17108,17178", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "4576,4662,4761,4864,4954,5267,5363,16191,16278,16447,16538,16692,16770,16848,16923,17103,17173,17294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4758,4845,4958,5038,5123,5224,5327,5421,5523,5609,5715,5811,5919,6036,6116,6222", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4753,4840,4953,5033,5118,5219,5322,5416,5518,5604,5710,5806,5914,6031,6111,6217,6314"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5431,5549,5666,5787,5901,6001,6100,6216,6352,6470,6618,6704,6806,6900,6998,7120,7240,7347,7482,7619,7754,7926,8055,8172,8290,8411,8506,8603,8721,8860,8963,9065,9176,9314,9454,9565,9668,9745,9840,9938,10048,10134,10221,10334,10414,10499,10600,10703,10797,10899,10985,11091,11187,11295,11412,11492,11598", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "5544,5661,5782,5896,5996,6095,6211,6347,6465,6613,6699,6801,6895,6993,7115,7235,7342,7477,7614,7749,7921,8050,8167,8285,8406,8501,8598,8716,8855,8958,9060,9171,9309,9449,9560,9663,9740,9835,9933,10043,10129,10216,10329,10409,10494,10595,10698,10792,10894,10980,11086,11182,11290,11407,11487,11593,11690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,16543", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,16620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae282b3edfeee1699c61b5e94e80ed2\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3545,3647,3746,3845,3951,4055,16928", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3540,3642,3741,3840,3946,4050,4168,17024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bf986b2bb756bc676c370bdeef3914\\transformed\\material-1.10.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1075,1172,1241,1304,1391,1455,1521,1581,1650,1711,1765,1880,1939,1999,2053,2125,2255,2343,2427,2565,2643,2719,2858,2952,3032,3088,3142,3208,3281,3359,3445,3529,3602,3680,3758,3833,3943,4033,4108,4202,4300,4374,4451,4551,4604,4688,4756,4845,4934,4996,5061,5124,5194,5301,5401,5501,5597,5657,5715", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "268,346,420,504,597,691,790,915,1003,1070,1167,1236,1299,1386,1450,1516,1576,1645,1706,1760,1875,1934,1994,2048,2120,2250,2338,2422,2560,2638,2714,2853,2947,3027,3083,3137,3203,3276,3354,3440,3524,3597,3675,3753,3828,3938,4028,4103,4197,4295,4369,4446,4546,4599,4683,4751,4840,4929,4991,5056,5119,5189,5296,5396,5496,5592,5652,5710,5790"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3104,3178,3262,3355,4173,4272,4397,4959,5026,5123,5368,11695,11782,11846,11912,11972,12041,12102,12156,12271,12330,12390,12444,12516,12646,12734,12818,12956,13034,13110,13249,13343,13423,13479,13533,13599,13672,13750,13836,13920,13993,14071,14149,14224,14334,14424,14499,14593,14691,14765,14842,14942,14995,15079,15147,15236,15325,15387,15452,15515,15585,15692,15792,15892,15988,16048,16283", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "318,3099,3173,3257,3350,3444,4267,4392,4480,5021,5118,5187,5426,11777,11841,11907,11967,12036,12097,12151,12266,12325,12385,12439,12511,12641,12729,12813,12951,13029,13105,13244,13338,13418,13474,13528,13594,13667,13745,13831,13915,13988,14066,14144,14219,14329,14419,14494,14588,14686,14760,14837,14937,14990,15074,15142,15231,15320,15382,15447,15510,15580,15687,15787,15887,15983,16043,16101,16358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74b732bc14271422d5c17044383b41bc\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "185,186", "startColumns": "4,4", "startOffsets": "17299,17387", "endColumns": "87,90", "endOffsets": "17382,17473"}}]}]}