{"logs": [{"outputFile": "cn.ykload.seeq.app-mergeDebugResources-59:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85171e4206c16718d7c8d5bf43fe825d\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,16760", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,16841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\74b732bc14271422d5c17044383b41bc\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,107", "endOffsets": "159,267"}, "to": {"startLines": "185,186", "startColumns": "4,4", "startOffsets": "17518,17627", "endColumns": "108,107", "endOffsets": "17622,17730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b3bf986b2bb756bc676c370bdeef3914\\transformed\\material-1.10.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1121,1211,1286,1345,1436,1499,1564,1623,1694,1756,1813,1932,1990,2051,2106,2179,2311,2402,2491,2632,2710,2787,2910,3002,3079,3137,3188,3254,3326,3408,3490,3568,3643,3717,3789,3868,3976,4073,4154,4240,4332,4406,4485,4571,4625,4701,4769,4852,4933,4995,5059,5122,5190,5302,5413,5517,5630,5691,5746", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "280,381,480,556,647,731,837,966,1051,1116,1206,1281,1340,1431,1494,1559,1618,1689,1751,1808,1927,1985,2046,2101,2174,2306,2397,2486,2627,2705,2782,2905,2997,3074,3132,3183,3249,3321,3403,3485,3563,3638,3712,3784,3863,3971,4068,4149,4235,4327,4401,4480,4566,4620,4696,4764,4847,4928,4990,5054,5117,5185,5297,5408,5512,5625,5686,5741,5823"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,3463,4289,4395,4524,5082,5147,5237,5495,11933,12024,12087,12152,12211,12282,12344,12401,12520,12578,12639,12694,12767,12899,12990,13079,13220,13298,13375,13498,13590,13667,13725,13776,13842,13914,13996,14078,14156,14231,14305,14377,14456,14564,14661,14742,14828,14920,14994,15073,15159,15213,15289,15357,15440,15521,15583,15647,15710,15778,15890,16001,16105,16218,16279,16505", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,58,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,173", "endColumns": "12,100,98,75,90,83,105,128,84,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,88,140,77,76,122,91,76,57,50,65,71,81,81,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81", "endOffsets": "330,3192,3291,3367,3458,3542,4390,4519,4604,5142,5232,5307,5549,12019,12082,12147,12206,12277,12339,12396,12515,12573,12634,12689,12762,12894,12985,13074,13215,13293,13370,13493,13585,13662,13720,13771,13837,13909,13991,14073,14151,14226,14300,14372,14451,14559,14656,14737,14823,14915,14989,15068,15154,15208,15284,15352,15435,15516,15578,15642,15705,15773,15885,15996,16100,16213,16274,16329,16582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\282188e067ace35e7ceeb03ac6735130\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,1019,1105,1180,1257,1330,1403,1484,1550", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,1014,1100,1175,1252,1325,1398,1479,1545,1671"}, "to": {"startLines": "48,49,50,51,52,56,57,171,172,174,175,177,178,179,180,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4609,4703,4791,4895,4999,5312,5396,16334,16423,16587,16674,16846,16921,16998,17071,17245,17326,17392", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,76,72,72,80,65,125", "endOffsets": "4698,4786,4890,4994,5077,5391,5490,16418,16500,16669,16755,16916,16993,17066,17139,17321,17387,17513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e754f461ad8f7a511fa7fc6c4385c693\\transformed\\material3-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,326,437,571,686,786,903,1052,1176,1339,1425,1524,1617,1719,1839,1966,2070,2196,2327,2471,2639,2761,2878,2997,3124,3218,3315,3446,3583,3685,3797,3902,4028,4157,4260,4363,4444,4542,4638,4746,4833,4919,5038,5118,5202,5302,5404,5500,5598,5685,5792,5891,5992,6113,6193,6316", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "187,321,432,566,681,781,898,1047,1171,1334,1420,1519,1612,1714,1834,1961,2065,2191,2322,2466,2634,2756,2873,2992,3119,3213,3310,3441,3578,3680,3792,3897,4023,4152,4255,4358,4439,4537,4633,4741,4828,4914,5033,5113,5197,5297,5399,5495,5593,5680,5787,5886,5987,6108,6188,6311,6429"}, "to": {"startLines": "59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5554,5691,5825,5936,6070,6185,6285,6402,6551,6675,6838,6924,7023,7116,7218,7338,7465,7569,7695,7826,7970,8138,8260,8377,8496,8623,8717,8814,8945,9082,9184,9296,9401,9527,9656,9759,9862,9943,10041,10137,10245,10332,10418,10537,10617,10701,10801,10903,10999,11097,11184,11291,11390,11491,11612,11692,11815", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "5686,5820,5931,6065,6180,6280,6397,6546,6670,6833,6919,7018,7111,7213,7333,7460,7564,7690,7821,7965,8133,8255,8372,8491,8618,8712,8809,8940,9077,9179,9291,9396,9522,9651,9754,9857,9938,10036,10132,10240,10327,10413,10532,10612,10696,10796,10898,10994,11092,11179,11286,11385,11486,11607,11687,11810,11928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae282b3edfeee1699c61b5e94e80ed2\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "38,39,40,41,42,43,44,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3547,3650,3754,3857,3959,4064,4170,17144", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3645,3749,3852,3954,4059,4165,4284,17240"}}]}]}