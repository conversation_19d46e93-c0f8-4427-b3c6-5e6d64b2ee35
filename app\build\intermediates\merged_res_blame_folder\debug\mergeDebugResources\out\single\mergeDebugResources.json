[{"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/xml_data_extraction_rules.xml.flat", "source": "cn.ykload.seeq.app-main-63:/xml/data_extraction_rules.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-hdpi_ic_launcher.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/xml_backup_rules.xml.flat", "source": "cn.ykload.seeq.app-main-63:/xml/backup_rules.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-mdpi_ic_launcher.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/xml_accessibility_service_config.xml.flat", "source": "cn.ykload.seeq.app-main-63:/xml/accessibility_service_config.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-anydpi_ic_launcher.xml.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/drawable_ic_launcher_foreground.xml.flat", "source": "cn.ykload.seeq.app-main-63:/drawable/ic_launcher_foreground.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "cn.ykload.seeq.app-debug-61:/drawable_ic_launcher_background.xml.flat", "source": "cn.ykload.seeq.app-main-63:/drawable/ic_launcher_background.xml"}, {"merged": "cn.ykload.seeq.app-debug-61:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "cn.ykload.seeq.app-main-63:/mipmap-xhdpi/ic_launcher_round.webp"}]