  AccessibilityService android.accessibilityservice  GestureDescription android.accessibilityservice  AccessibilityNodeInfo 1android.accessibilityservice.AccessibilityService  	Exception 1android.accessibilityservice.AccessibilityService  GestureDescription 1android.accessibilityservice.AccessibilityService  GestureResultCallback 1android.accessibilityservice.AccessibilityService  Handler 1android.accessibilityservice.AccessibilityService  Intent 1android.accessibilityservice.AccessibilityService  Log 1android.accessibilityservice.AccessibilityService  Looper 1android.accessibilityservice.AccessibilityService  Path 1android.accessibilityservice.AccessibilityService  TAG 1android.accessibilityservice.AccessibilityService  contains 1android.accessibilityservice.AccessibilityService  dispatchGesture 1android.accessibilityservice.AccessibilityService  instance 1android.accessibilityservice.AccessibilityService  isAutomationRunning 1android.accessibilityservice.AccessibilityService  let 1android.accessibilityservice.AccessibilityService  onServiceConnected 1android.accessibilityservice.AccessibilityService  onUnbind 1android.accessibilityservice.AccessibilityService  repeat 1android.accessibilityservice.AccessibilityService  rootInActiveWindow 1android.accessibilityservice.AccessibilityService  until 1android.accessibilityservice.AccessibilityService  Log Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  TAG Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  Builder /android.accessibilityservice.GestureDescription  StrokeDescription /android.accessibilityservice.GestureDescription  	addStroke 7android.accessibilityservice.GestureDescription.Builder  build 7android.accessibilityservice.GestureDescription.Builder  Activity android.app  AlertDialog android.app  
MainScreen android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  	SeeqTheme android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Builder android.app.AlertDialog  
setMessage android.app.AlertDialog.Builder  setNegativeButton android.app.AlertDialog.Builder  setPositiveButton android.app.AlertDialog.Builder  setTitle android.app.AlertDialog.Builder  show android.app.AlertDialog.Builder  AccessibilityNodeInfo android.app.Service  	Exception android.app.Service  GestureDescription android.app.Service  GestureResultCallback android.app.Service  Handler android.app.Service  Intent android.app.Service  Log android.app.Service  Looper android.app.Service  Path android.app.Service  TAG android.app.Service  contains android.app.Service  instance android.app.Service  isAutomationRunning android.app.Service  let android.app.Service  onUnbind android.app.Service  repeat android.app.Service  until android.app.Service  Context android.content  Intent android.content  AccessibilityNodeInfo android.content.Context  	Exception android.content.Context  GestureDescription android.content.Context  GestureResultCallback android.content.Context  Handler android.content.Context  Intent android.content.Context  Log android.content.Context  Looper android.content.Context  
MainScreen android.content.Context  Modifier android.content.Context  Path android.content.Context  Scaffold android.content.Context  	SeeqTheme android.content.Context  TAG android.content.Context  contains android.content.Context  contentResolver android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  	getString android.content.Context  instance android.content.Context  isAutomationRunning android.content.Context  let android.content.Context  packageManager android.content.Context  packageName android.content.Context  padding android.content.Context  repeat android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  until android.content.Context  AccessibilityNodeInfo android.content.ContextWrapper  	Exception android.content.ContextWrapper  GestureDescription android.content.ContextWrapper  GestureResultCallback android.content.ContextWrapper  Handler android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  Modifier android.content.ContextWrapper  Path android.content.ContextWrapper  Scaffold android.content.ContextWrapper  	SeeqTheme android.content.ContextWrapper  TAG android.content.ContextWrapper  contains android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  instance android.content.ContextWrapper  isAutomationRunning android.content.ContextWrapper  let android.content.ContextWrapper  packageManager android.content.ContextWrapper  padding android.content.ContextWrapper  repeat android.content.ContextWrapper  
setContent android.content.ContextWrapper  
startActivity android.content.ContextWrapper  until android.content.ContextWrapper  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  FLAG_ACTIVITY_NEW_TASK android.content.Intent  addFlags android.content.Intent  PackageInfo android.content.pm  getLaunchIntentForPackage !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  Path android.graphics  Rect android.graphics  lineTo android.graphics.Path  moveTo android.graphics.Path  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  postDelayed android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings  SettingNotFoundException android.provider.Settings  ACCESSIBILITY_ENABLED  android.provider.Settings.Secure  ENABLED_ACCESSIBILITY_SERVICES  android.provider.Settings.Secure  getInt  android.provider.Settings.Secure  	getString  android.provider.Settings.Secure  	TextUtils android.text  isEmpty android.text.TextUtils  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  
MainScreen  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  	SeeqTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  AccessibilityEvent android.view.accessibility  AccessibilityNodeInfo android.view.accessibility  	eventType -android.view.accessibility.AccessibilityEvent  let -android.view.accessibility.AccessibilityEvent  packageName -android.view.accessibility.AccessibilityEvent  ACTION_CLICK 0android.view.accessibility.AccessibilityNodeInfo  ACTION_SCROLL_FORWARD 0android.view.accessibility.AccessibilityNodeInfo  
childCount 0android.view.accessibility.AccessibilityNodeInfo  	className 0android.view.accessibility.AccessibilityNodeInfo  contentDescription 0android.view.accessibility.AccessibilityNodeInfo  getChild 0android.view.accessibility.AccessibilityNodeInfo  isClickable 0android.view.accessibility.AccessibilityNodeInfo  isScrollable 0android.view.accessibility.AccessibilityNodeInfo  parent 0android.view.accessibility.AccessibilityNodeInfo  
performAction 0android.view.accessibility.AccessibilityNodeInfo  recycle 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
MainScreen #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  	SeeqTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  AccessibilityPermissionHelper "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  	SeeqTheme "androidx.compose.foundation.layout  SeqAccessibilityService "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  isAccessibilityServiceEnabled "androidx.compose.foundation.layout  isHeytapHeadsetInstalled "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  openAccessibilitySettings "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  showPermissionGuideDialog "androidx.compose.foundation.layout  stringResource "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  AccessibilityPermissionHelper .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  SeqAccessibilityService .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  isAccessibilityServiceEnabled .androidx.compose.foundation.layout.ColumnScope  isHeytapHeadsetInstalled .androidx.compose.foundation.layout.ColumnScope  openAccessibilitySettings .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  showPermissionGuideDialog .androidx.compose.foundation.layout.ColumnScope  stringResource .androidx.compose.foundation.layout.ColumnScope  R +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  stringResource +androidx.compose.foundation.layout.RowScope  AccessibilityPermissionHelper androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  LaunchedEffect androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Preview androidx.compose.material3  R androidx.compose.material3  Scaffold androidx.compose.material3  	SeeqTheme androidx.compose.material3  SeqAccessibilityService androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  isAccessibilityServiceEnabled androidx.compose.material3  isHeytapHeadsetInstalled androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  openAccessibilitySettings androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  showPermissionGuideDialog androidx.compose.material3  stringResource androidx.compose.material3  error &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  AccessibilityPermissionHelper androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
MainScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  R androidx.compose.runtime  Scaffold androidx.compose.runtime  	SeeqTheme androidx.compose.runtime  SeqAccessibilityService androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  isAccessibilityServiceEnabled androidx.compose.runtime  isHeytapHeadsetInstalled androidx.compose.runtime  mutableStateOf androidx.compose.runtime  openAccessibilitySettings androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  showPermissionGuideDialog androidx.compose.runtime  stringResource androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  	SeeqTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AccessibilityEvent cn.ykload.seeq  AccessibilityNodeInfo cn.ykload.seeq  AccessibilityPermissionHelper cn.ykload.seeq  AccessibilityService cn.ykload.seeq  AlertDialog cn.ykload.seeq  	Alignment cn.ykload.seeq  Arrangement cn.ykload.seeq  Boolean cn.ykload.seeq  Bundle cn.ykload.seeq  Button cn.ykload.seeq  Card cn.ykload.seeq  Column cn.ykload.seeq  ComponentActivity cn.ykload.seeq  
Composable cn.ykload.seeq  Context cn.ykload.seeq  	Exception cn.ykload.seeq  Float cn.ykload.seeq  GestureDescription cn.ykload.seeq  GestureResultCallback cn.ykload.seeq  Handler cn.ykload.seeq  Int cn.ykload.seeq  Intent cn.ykload.seeq  LaunchedEffect cn.ykload.seeq  Log cn.ykload.seeq  Looper cn.ykload.seeq  MainActivity cn.ykload.seeq  
MainScreen cn.ykload.seeq  MainScreenPreview cn.ykload.seeq  
MaterialTheme cn.ykload.seeq  Modifier cn.ykload.seeq  Path cn.ykload.seeq  Preview cn.ykload.seeq  R cn.ykload.seeq  Scaffold cn.ykload.seeq  	SeeqTheme cn.ykload.seeq  SeqAccessibilityService cn.ykload.seeq  Settings cn.ykload.seeq  String cn.ykload.seeq  TAG cn.ykload.seeq  Text cn.ykload.seeq  
TextButton cn.ykload.seeq  	TextUtils cn.ykload.seeq  Unit cn.ykload.seeq  contains cn.ykload.seeq  fillMaxSize cn.ykload.seeq  fillMaxWidth cn.ykload.seeq  getValue cn.ykload.seeq  instance cn.ykload.seeq  isAccessibilityServiceEnabled cn.ykload.seeq  isAutomationRunning cn.ykload.seeq  isHeytapHeadsetInstalled cn.ykload.seeq  java cn.ykload.seeq  let cn.ykload.seeq  mutableStateOf cn.ykload.seeq  openAccessibilitySettings cn.ykload.seeq  padding cn.ykload.seeq  provideDelegate cn.ykload.seeq  remember cn.ykload.seeq  repeat cn.ykload.seeq  setValue cn.ykload.seeq  showPermissionGuideDialog cn.ykload.seeq  stringResource cn.ykload.seeq  until cn.ykload.seeq  AlertDialog ,cn.ykload.seeq.AccessibilityPermissionHelper  Intent ,cn.ykload.seeq.AccessibilityPermissionHelper  Log ,cn.ykload.seeq.AccessibilityPermissionHelper  SeqAccessibilityService ,cn.ykload.seeq.AccessibilityPermissionHelper  Settings ,cn.ykload.seeq.AccessibilityPermissionHelper  TAG ,cn.ykload.seeq.AccessibilityPermissionHelper  	TextUtils ,cn.ykload.seeq.AccessibilityPermissionHelper  contains ,cn.ykload.seeq.AccessibilityPermissionHelper  isAccessibilityServiceEnabled ,cn.ykload.seeq.AccessibilityPermissionHelper  isHeytapHeadsetInstalled ,cn.ykload.seeq.AccessibilityPermissionHelper  java ,cn.ykload.seeq.AccessibilityPermissionHelper  openAccessibilitySettings ,cn.ykload.seeq.AccessibilityPermissionHelper  showPermissionGuideDialog ,cn.ykload.seeq.AccessibilityPermissionHelper  
MainScreen cn.ykload.seeq.MainActivity  Modifier cn.ykload.seeq.MainActivity  Scaffold cn.ykload.seeq.MainActivity  	SeeqTheme cn.ykload.seeq.MainActivity  enableEdgeToEdge cn.ykload.seeq.MainActivity  fillMaxSize cn.ykload.seeq.MainActivity  padding cn.ykload.seeq.MainActivity  
setContent cn.ykload.seeq.MainActivity  app_name cn.ykload.seeq.R.string  automation_running cn.ykload.seeq.R.string  check_permission cn.ykload.seeq.R.string  permission_granted cn.ykload.seeq.R.string  permission_required cn.ykload.seeq.R.string  start_automation cn.ykload.seeq.R.string  AccessibilityEvent &cn.ykload.seeq.SeqAccessibilityService  AccessibilityNodeInfo &cn.ykload.seeq.SeqAccessibilityService  Boolean &cn.ykload.seeq.SeqAccessibilityService  	Companion &cn.ykload.seeq.SeqAccessibilityService  	Exception &cn.ykload.seeq.SeqAccessibilityService  Float &cn.ykload.seeq.SeqAccessibilityService  GestureDescription &cn.ykload.seeq.SeqAccessibilityService  GestureResultCallback &cn.ykload.seeq.SeqAccessibilityService  Handler &cn.ykload.seeq.SeqAccessibilityService  Int &cn.ykload.seeq.SeqAccessibilityService  Intent &cn.ykload.seeq.SeqAccessibilityService  Log &cn.ykload.seeq.SeqAccessibilityService  Looper &cn.ykload.seeq.SeqAccessibilityService  Path &cn.ykload.seeq.SeqAccessibilityService  SeqAccessibilityService &cn.ykload.seeq.SeqAccessibilityService  String &cn.ykload.seeq.SeqAccessibilityService  TAG &cn.ykload.seeq.SeqAccessibilityService  contains &cn.ykload.seeq.SeqAccessibilityService  currentStep &cn.ykload.seeq.SeqAccessibilityService  dispatchGesture &cn.ykload.seeq.SeqAccessibilityService  findMasterTuning &cn.ykload.seeq.SeqAccessibilityService  findNodeByText &cn.ykload.seeq.SeqAccessibilityService  findScrollableNode &cn.ykload.seeq.SeqAccessibilityService  findSeeq &cn.ykload.seeq.SeqAccessibilityService  handleHeytapHeadsetEvent &cn.ykload.seeq.SeqAccessibilityService  handler &cn.ykload.seeq.SeqAccessibilityService  instance &cn.ykload.seeq.SeqAccessibilityService  isAutomationRunning &cn.ykload.seeq.SeqAccessibilityService  let &cn.ykload.seeq.SeqAccessibilityService  openHeytapHeadsetApp &cn.ykload.seeq.SeqAccessibilityService  packageManager &cn.ykload.seeq.SeqAccessibilityService  performSwipeGesture &cn.ykload.seeq.SeqAccessibilityService  
printNodeTree &cn.ykload.seeq.SeqAccessibilityService  repeat &cn.ykload.seeq.SeqAccessibilityService  rootInActiveWindow &cn.ykload.seeq.SeqAccessibilityService  
safeClickNode &cn.ykload.seeq.SeqAccessibilityService  
scrollDown &cn.ykload.seeq.SeqAccessibilityService  
startActivity &cn.ykload.seeq.SeqAccessibilityService  startAutomation &cn.ykload.seeq.SeqAccessibilityService  until &cn.ykload.seeq.SeqAccessibilityService  waitForNoiseControl &cn.ykload.seeq.SeqAccessibilityService  AccessibilityNodeInfo 0cn.ykload.seeq.SeqAccessibilityService.Companion  GestureDescription 0cn.ykload.seeq.SeqAccessibilityService.Companion  Handler 0cn.ykload.seeq.SeqAccessibilityService.Companion  Intent 0cn.ykload.seeq.SeqAccessibilityService.Companion  Log 0cn.ykload.seeq.SeqAccessibilityService.Companion  Looper 0cn.ykload.seeq.SeqAccessibilityService.Companion  Path 0cn.ykload.seeq.SeqAccessibilityService.Companion  TAG 0cn.ykload.seeq.SeqAccessibilityService.Companion  contains 0cn.ykload.seeq.SeqAccessibilityService.Companion  instance 0cn.ykload.seeq.SeqAccessibilityService.Companion  isAutomationRunning 0cn.ykload.seeq.SeqAccessibilityService.Companion  let 0cn.ykload.seeq.SeqAccessibilityService.Companion  repeat 0cn.ykload.seeq.SeqAccessibilityService.Companion  until 0cn.ykload.seeq.SeqAccessibilityService.Companion  SettingNotFoundException cn.ykload.seeq.Settings  Boolean cn.ykload.seeq.ui.theme  Build cn.ykload.seeq.ui.theme  
Composable cn.ykload.seeq.ui.theme  DarkColorScheme cn.ykload.seeq.ui.theme  
FontFamily cn.ykload.seeq.ui.theme  
FontWeight cn.ykload.seeq.ui.theme  LightColorScheme cn.ykload.seeq.ui.theme  Pink40 cn.ykload.seeq.ui.theme  Pink80 cn.ykload.seeq.ui.theme  Purple40 cn.ykload.seeq.ui.theme  Purple80 cn.ykload.seeq.ui.theme  PurpleGrey40 cn.ykload.seeq.ui.theme  PurpleGrey80 cn.ykload.seeq.ui.theme  	SeeqTheme cn.ykload.seeq.ui.theme  
Typography cn.ykload.seeq.ui.theme  Unit cn.ykload.seeq.ui.theme  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  name java.lang.Class  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  let kotlin  repeat kotlin  toString 
kotlin.Any  not kotlin.Boolean  toString kotlin.CharSequence  sp 
kotlin.Double  	compareTo 
kotlin.Int  plus 
kotlin.Int  contains 
kotlin.String  repeat 
kotlin.String  IntIterator kotlin.collections  contains kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  SuspendFunction1 kotlin.coroutines  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  contains kotlin.sequences  contains kotlin.text  repeat kotlin.text  CoroutineScope kotlinx.coroutines  AccessibilityPermissionHelper !kotlinx.coroutines.CoroutineScope  R !kotlinx.coroutines.CoroutineScope  isAccessibilityServiceEnabled !kotlinx.coroutines.CoroutineScope  isHeytapHeadsetInstalled !kotlinx.coroutines.CoroutineScope  Rect 1android.accessibilityservice.AccessibilityService  Triple 1android.accessibilityservice.AccessibilityService  arrayOf 1android.accessibilityservice.AccessibilityService  
isNotEmpty 1android.accessibilityservice.AccessibilityService  kotlin 1android.accessibilityservice.AccessibilityService  
mutableListOf 1android.accessibilityservice.AccessibilityService  Rect android.app.Service  Triple android.app.Service  arrayOf android.app.Service  
isNotEmpty android.app.Service  kotlin android.app.Service  
mutableListOf android.app.Service  Rect android.content.Context  Triple android.content.Context  arrayOf android.content.Context  
isNotEmpty android.content.Context  kotlin android.content.Context  
mutableListOf android.content.Context  Rect android.content.ContextWrapper  Triple android.content.ContextWrapper  arrayOf android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  kotlin android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  	resources android.content.ContextWrapper  displayMetrics android.content.res.Resources  bottom android.graphics.Rect  centerX android.graphics.Rect  centerY android.graphics.Rect  height android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  getBoundsInScreen 0android.view.accessibility.AccessibilityNodeInfo  Int "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  getStepDescription "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  Spacer .androidx.compose.foundation.layout.ColumnScope  getStepDescription .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  Int androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  getStepDescription androidx.compose.material3  height androidx.compose.material3  onSurfaceVariant &androidx.compose.material3.ColorScheme  
bodyMedium %androidx.compose.material3.Typography  Int androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  getStepDescription androidx.compose.runtime  height androidx.compose.runtime  height androidx.compose.ui.Modifier  height &androidx.compose.ui.Modifier.Companion  Array cn.ykload.seeq  List cn.ykload.seeq  MutableList cn.ykload.seeq  Rect cn.ykload.seeq  Spacer cn.ykload.seeq  Triple cn.ykload.seeq  arrayOf cn.ykload.seeq  getStepDescription cn.ykload.seeq  height cn.ykload.seeq  
isNotEmpty cn.ykload.seeq  kotlin cn.ykload.seeq  
mutableListOf cn.ykload.seeq  Array &cn.ykload.seeq.SeqAccessibilityService  List &cn.ykload.seeq.SeqAccessibilityService  MutableList &cn.ykload.seeq.SeqAccessibilityService  Rect &cn.ykload.seeq.SeqAccessibilityService  Triple &cn.ykload.seeq.SeqAccessibilityService  adjustEQByCoordinates &cn.ykload.seeq.SeqAccessibilityService  adjustEQSlider &cn.ykload.seeq.SeqAccessibilityService  adjustEqualizer &cn.ykload.seeq.SeqAccessibilityService  adjustFrequencyBand &cn.ykload.seeq.SeqAccessibilityService  adjustFrequencyBandSequentially &cn.ykload.seeq.SeqAccessibilityService  arrayOf &cn.ykload.seeq.SeqAccessibilityService  findEQChartNode &cn.ykload.seeq.SeqAccessibilityService  findEQChartNodeRecursive &cn.ykload.seeq.SeqAccessibilityService  findEQControlNodes &cn.ykload.seeq.SeqAccessibilityService  findEQControlNodesRecursive &cn.ykload.seeq.SeqAccessibilityService  getCurrentStep &cn.ykload.seeq.SeqAccessibilityService  
isNotEmpty &cn.ykload.seeq.SeqAccessibilityService  kotlin &cn.ykload.seeq.SeqAccessibilityService  
mutableListOf &cn.ykload.seeq.SeqAccessibilityService  performClickGesture &cn.ykload.seeq.SeqAccessibilityService  performDragGesture &cn.ykload.seeq.SeqAccessibilityService  	resources &cn.ykload.seeq.SeqAccessibilityService  Rect 0cn.ykload.seeq.SeqAccessibilityService.Companion  Triple 0cn.ykload.seeq.SeqAccessibilityService.Companion  arrayOf 0cn.ykload.seeq.SeqAccessibilityService.Companion  
isNotEmpty 0cn.ykload.seeq.SeqAccessibilityService.Companion  kotlin 0cn.ykload.seeq.SeqAccessibilityService.Companion  
mutableListOf 0cn.ykload.seeq.SeqAccessibilityService.Companion  Array kotlin  Triple kotlin  arrayOf kotlin  get kotlin.Array  size kotlin.Array  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  minus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  List kotlin.collections  MutableList kotlin.collections  
isNotEmpty kotlin.collections  
mutableListOf kotlin.collections  get kotlin.collections.List  
isNotEmpty kotlin.collections.List  size kotlin.collections.List  add kotlin.collections.MutableList  size kotlin.collections.MutableList  kotlin 
kotlin.jvm  sqrt kotlin.math  KClass kotlin.reflect  
isNotEmpty kotlin.text  Pair 1android.accessibilityservice.AccessibilityService  Pair android.app.Service  Pair android.content.Context  Pair android.content.ContextWrapper  Pair cn.ykload.seeq  Pair &cn.ykload.seeq.SeqAccessibilityService  Pair 0cn.ykload.seeq.SeqAccessibilityService.Companion  Pair kotlin  
component1 kotlin.Pair  
component2 kotlin.Pair  Regex 1android.accessibilityservice.AccessibilityService  matches 1android.accessibilityservice.AccessibilityService  Regex android.app.Service  matches android.app.Service  Regex android.content.Context  matches android.content.Context  Regex android.content.ContextWrapper  matches android.content.ContextWrapper  	isEnabled 0android.view.accessibility.AccessibilityNodeInfo  isFocusable 0android.view.accessibility.AccessibilityNodeInfo  Regex cn.ykload.seeq  matches cn.ykload.seeq  Regex &cn.ykload.seeq.SeqAccessibilityService  analyzeEQInterface &cn.ykload.seeq.SeqAccessibilityService  debugAnalyzeCurrentInterface &cn.ykload.seeq.SeqAccessibilityService  findPotentialEQNodes &cn.ykload.seeq.SeqAccessibilityService  matches &cn.ykload.seeq.SeqAccessibilityService  Regex 0cn.ykload.seeq.SeqAccessibilityService.Companion  matches 0cn.ykload.seeq.SeqAccessibilityService.Companion  matches 
kotlin.String  Regex kotlin.text  matches kotlin.text  isLikelyEQInterface &cn.ykload.seeq.SeqAccessibilityService  
EQCoordinates 1android.accessibilityservice.AccessibilityService  
FloatArray 1android.accessibilityservice.AccessibilityService  contentToString 1android.accessibilityservice.AccessibilityService  indices 1android.accessibilityservice.AccessibilityService  toTypedArray 1android.accessibilityservice.AccessibilityService  
EQCoordinates android.app.Service  
FloatArray android.app.Service  contentToString android.app.Service  indices android.app.Service  toTypedArray android.app.Service  
EQCoordinates android.content.Context  
FloatArray android.content.Context  contentToString android.content.Context  indices android.content.Context  toTypedArray android.content.Context  
EQCoordinates android.content.ContextWrapper  
FloatArray android.content.ContextWrapper  contentToString android.content.ContextWrapper  indices android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  Any cn.ykload.seeq  
EQCoordinates cn.ykload.seeq  
FloatArray cn.ykload.seeq  
contentEquals cn.ykload.seeq  contentHashCode cn.ykload.seeq  contentToString cn.ykload.seeq  indices cn.ykload.seeq  	javaClass cn.ykload.seeq  toTypedArray cn.ykload.seeq  bandCenterX cn.ykload.seeq.EQCoordinates  
contentEquals cn.ykload.seeq.EQCoordinates  contentHashCode cn.ykload.seeq.EQCoordinates  	eqBottomY cn.ykload.seeq.EQCoordinates  eqHeight cn.ykload.seeq.EQCoordinates  eqTopY cn.ykload.seeq.EQCoordinates  	javaClass cn.ykload.seeq.EQCoordinates  
EQCoordinates &cn.ykload.seeq.SeqAccessibilityService  
FloatArray &cn.ykload.seeq.SeqAccessibilityService  adjustFrequencyBandByClick &cn.ykload.seeq.SeqAccessibilityService  containsEQComponents &cn.ykload.seeq.SeqAccessibilityService  containsEQComponentsRecursive &cn.ykload.seeq.SeqAccessibilityService  contentToString &cn.ykload.seeq.SeqAccessibilityService  findEQControlArea &cn.ykload.seeq.SeqAccessibilityService  findEQControlAreaRecursive &cn.ykload.seeq.SeqAccessibilityService  getEQCoordinates &cn.ykload.seeq.SeqAccessibilityService  getFallbackEQCoordinates &cn.ykload.seeq.SeqAccessibilityService  indices &cn.ykload.seeq.SeqAccessibilityService  printEQNodeDetails &cn.ykload.seeq.SeqAccessibilityService  toTypedArray &cn.ykload.seeq.SeqAccessibilityService  
EQCoordinates 0cn.ykload.seeq.SeqAccessibilityService.Companion  
FloatArray 0cn.ykload.seeq.SeqAccessibilityService.Companion  contentToString 0cn.ykload.seeq.SeqAccessibilityService.Companion  indices 0cn.ykload.seeq.SeqAccessibilityService.Companion  toTypedArray 0cn.ykload.seeq.SeqAccessibilityService.Companion  
FloatArray kotlin  hashCode 
kotlin.Any  	javaClass 
kotlin.Any  
contentEquals kotlin.Array  contentHashCode kotlin.Array  indices kotlin.Array  iterator kotlin.Array  hashCode kotlin.Float  contentToString kotlin.FloatArray  get kotlin.FloatArray  set kotlin.FloatArray  toTypedArray kotlin.FloatArray  inc 
kotlin.Int  rangeTo 
kotlin.Int  Iterator kotlin.collections  
contentEquals kotlin.collections  contentHashCode kotlin.collections  contentToString kotlin.collections  indices kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  	javaClass 
kotlin.jvm  
contentEquals kotlin.text  indices kotlin.text  containsModalElements &cn.ykload.seeq.SeqAccessibilityService  findEQControlAreaInModal &cn.ykload.seeq.SeqAccessibilityService  findEQModal &cn.ykload.seeq.SeqAccessibilityService  findEQModalRecursive &cn.ykload.seeq.SeqAccessibilityService  findNodeByTextInSubtree &cn.ykload.seeq.SeqAccessibilityService   findNodeByTextInSubtreeRecursive &cn.ykload.seeq.SeqAccessibilityService  waitForEQModal &cn.ykload.seeq.SeqAccessibilityService  analyzeCurrentEQSettings &cn.ykload.seeq.SeqAccessibilityService  analyzeEQLayout &cn.ykload.seeq.SeqAccessibilityService  findAllNodesByText &cn.ykload.seeq.SeqAccessibilityService  findAllNodesByTextRecursive &cn.ykload.seeq.SeqAccessibilityService  findAndAnalyzeEQComponents &cn.ykload.seeq.SeqAccessibilityService  findEQComponentsRecursive &cn.ykload.seeq.SeqAccessibilityService  findSliderControls &cn.ykload.seeq.SeqAccessibilityService  findSliderControlsRecursive &cn.ykload.seeq.SeqAccessibilityService  indices kotlin.collections.List  contains kotlin.ranges.IntRange  contains android.graphics.Rect  
intersects android.graphics.Rect  detectCurrentEQControlPoints &cn.ykload.seeq.SeqAccessibilityService  findControlPointsInArea &cn.ykload.seeq.SeqAccessibilityService   findControlPointsInAreaRecursive &cn.ykload.seeq.SeqAccessibilityService  
isNotEmpty 
kotlin.String  Math 1android.accessibilityservice.AccessibilityService  android 1android.accessibilityservice.AccessibilityService  any 1android.accessibilityservice.AccessibilityService  listOf 1android.accessibilityservice.AccessibilityService  Math android.app.Service  android android.app.Service  any android.app.Service  listOf android.app.Service  Math android.content.Context  android android.content.Context  any android.content.Context  listOf android.content.Context  Math android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  listOf android.content.ContextWrapper  MANUFACTURER android.os.Build  MODEL android.os.Build  RELEASE android.os.Build.VERSION  DisplayMetrics android.util  density android.util.DisplayMetrics  
densityDpi android.util.DisplayMetrics  DisplayMetrics cn.ykload.seeq  Math cn.ykload.seeq  android cn.ykload.seeq  any cn.ykload.seeq  listOf cn.ykload.seeq  CLICK_DELAY &cn.ykload.seeq.SeqAccessibilityService  DETECTION_INTERVAL &cn.ykload.seeq.SeqAccessibilityService  DisplayMetrics &cn.ykload.seeq.SeqAccessibilityService  MODAL_WAIT_DELAY &cn.ykload.seeq.SeqAccessibilityService  Math &cn.ykload.seeq.SeqAccessibilityService  
STEP_DELAY &cn.ykload.seeq.SeqAccessibilityService  android &cn.ykload.seeq.SeqAccessibilityService  any &cn.ykload.seeq.SeqAccessibilityService  getScreenSizeCategory &cn.ykload.seeq.SeqAccessibilityService  listOf &cn.ykload.seeq.SeqAccessibilityService  performCompatibilityCheck &cn.ykload.seeq.SeqAccessibilityService  Math 0cn.ykload.seeq.SeqAccessibilityService.Companion  android 0cn.ykload.seeq.SeqAccessibilityService.Companion  any 0cn.ykload.seeq.SeqAccessibilityService.Companion  listOf 0cn.ykload.seeq.SeqAccessibilityService.Companion  abs java.lang.Math  div 
kotlin.Int  div kotlin.Long  any kotlin.collections  listOf kotlin.collections  any kotlin.collections.List  any kotlin.sequences  any kotlin.text  Array "androidx.compose.foundation.layout  EQBandSlider "androidx.compose.foundation.layout  EQSettingsPanel "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Slider "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  arrayOf "androidx.compose.foundation.layout  copyOf "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  rangeTo "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Arrangement .androidx.compose.foundation.layout.ColumnScope  EQBandSlider .androidx.compose.foundation.layout.ColumnScope  EQSettingsPanel .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  arrayOf .androidx.compose.foundation.layout.ColumnScope  copyOf .androidx.compose.foundation.layout.ColumnScope  forEachIndexed .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Slider +androidx.compose.foundation.layout.RowScope  arrayOf +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  rangeTo +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  Array androidx.compose.material3  EQBandSlider androidx.compose.material3  EQSettingsPanel androidx.compose.material3  Row androidx.compose.material3  Slider androidx.compose.material3  Unit androidx.compose.material3  arrayOf androidx.compose.material3  copyOf androidx.compose.material3  forEachIndexed androidx.compose.material3  rangeTo androidx.compose.material3  spacedBy androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  	bodySmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  Array androidx.compose.runtime  EQBandSlider androidx.compose.runtime  EQSettingsPanel androidx.compose.runtime  Row androidx.compose.runtime  Slider androidx.compose.runtime  Unit androidx.compose.runtime  arrayOf androidx.compose.runtime  copyOf androidx.compose.runtime  forEachIndexed androidx.compose.runtime  rangeTo androidx.compose.runtime  spacedBy androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  CenterVertically androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  CenterVertically 'androidx.compose.ui.Alignment.Companion  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  EQBandSlider cn.ykload.seeq  EQSettingsPanel cn.ykload.seeq  Row cn.ykload.seeq  Slider cn.ykload.seeq  copyOf cn.ykload.seeq  forEachIndexed cn.ykload.seeq  rangeTo cn.ykload.seeq  spacedBy cn.ykload.seeq  weight cn.ykload.seeq  width cn.ykload.seeq  checkAndHandleCancelButton &cn.ykload.seeq.SeqAccessibilityService  
customEQGains &cn.ykload.seeq.SeqAccessibilityService  getCurrentEQGains &cn.ykload.seeq.SeqAccessibilityService  setCustomEQGains &cn.ykload.seeq.SeqAccessibilityService  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  IntArray kotlin  	LongArray kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  contentToString kotlin.Array  copyOf kotlin.Array  forEachIndexed kotlin.Array  set kotlin.Array  rangeTo kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function1  copyOf kotlin.collections  forEachIndexed kotlin.collections  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  rangeTo 
kotlin.ranges  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  contentToString "androidx.compose.foundation.layout  kotlin "androidx.compose.foundation.layout  contentToString .androidx.compose.foundation.layout.ColumnScope  kotlin +androidx.compose.foundation.layout.RowScope  contentToString androidx.compose.material3  kotlin androidx.compose.material3  contentToString androidx.compose.runtime  kotlin androidx.compose.runtime  round kotlin.math  joinToString 1android.accessibilityservice.AccessibilityService  mapOf 1android.accessibilityservice.AccessibilityService  take 1android.accessibilityservice.AccessibilityService  to 1android.accessibilityservice.AccessibilityService  joinToString android.app.Service  mapOf android.app.Service  take android.app.Service  to android.app.Service  joinToString android.content.Context  mapOf android.content.Context  take android.content.Context  to android.content.Context  joinToString android.content.ContextWrapper  mapOf android.content.ContextWrapper  take android.content.ContextWrapper  to android.content.ContextWrapper  joinToString cn.ykload.seeq  mapOf cn.ykload.seeq  take cn.ykload.seeq  to cn.ykload.seeq  detectCurrentStep &cn.ykload.seeq.SeqAccessibilityService  executeStepAction &cn.ykload.seeq.SeqAccessibilityService  findAndClickMasterTuning &cn.ykload.seeq.SeqAccessibilityService  findAndClickNoiseControl &cn.ykload.seeq.SeqAccessibilityService  findAndClickSeeq &cn.ykload.seeq.SeqAccessibilityService  joinToString &cn.ykload.seeq.SeqAccessibilityService  mapOf &cn.ykload.seeq.SeqAccessibilityService  performSmartDetection &cn.ykload.seeq.SeqAccessibilityService  take &cn.ykload.seeq.SeqAccessibilityService  to &cn.ykload.seeq.SeqAccessibilityService  waitForEQModalStable &cn.ykload.seeq.SeqAccessibilityService  joinToString 0cn.ykload.seeq.SeqAccessibilityService.Companion  mapOf 0cn.ykload.seeq.SeqAccessibilityService.Companion  take 0cn.ykload.seeq.SeqAccessibilityService.Companion  to 0cn.ykload.seeq.SeqAccessibilityService.Companion  to kotlin  take kotlin.Array  Map kotlin.collections  joinToString kotlin.collections  mapOf kotlin.collections  take kotlin.collections  joinToString kotlin.collections.List  get kotlin.collections.Map  Sequence kotlin.sequences  joinToString kotlin.sequences  take kotlin.sequences  take kotlin.text  EQ_MODAL_FORCE_WAIT &cn.ykload.seeq.SeqAccessibilityService  MAX_EQ_ADJUSTMENTS &cn.ykload.seeq.SeqAccessibilityService  eqAdjustmentCount &cn.ykload.seeq.SeqAccessibilityService  times kotlin.Long                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  