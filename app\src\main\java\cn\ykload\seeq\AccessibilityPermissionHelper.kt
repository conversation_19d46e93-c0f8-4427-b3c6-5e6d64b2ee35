package cn.ykload.seeq

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.text.TextUtils
import android.util.Log

object AccessibilityPermissionHelper {

    private const val TAG = "AccessibilityPermissionHelper"

    /**
     * 检查无障碍权限是否已开启
     */
    fun isAccessibilityServiceEnabled(context: Context): <PERSON>olean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            Log.e(TAG, "无法获取无障碍设置", e)
            0
        }

        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )

            if (!TextUtils.isEmpty(services)) {
                val serviceName = "${context.packageName}/${SeqAccessibilityService::class.java.name}"
                Log.d(TAG, "查找服务: $serviceName")
                Log.d(TAG, "已启用的服务: $services")
                return services.contains(serviceName)
            }
        }

        return false
    }

    /**
     * 打开无障碍设置页面
     */
    fun openAccessibilitySettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.d(TAG, "打开无障碍设置页面")
        } catch (e: Exception) {
            Log.e(TAG, "无法打开无障碍设置页面", e)
        }
    }

    /**
     * 显示权限引导对话框
     */
    fun showPermissionGuideDialog(context: Context) {
        AlertDialog.Builder(context)
            .setTitle("需要无障碍权限")
            .setMessage("为了实现自动化操作，需要开启无障碍权限。\n\n请在设置中找到Seeq服务并开启。")
            .setPositiveButton("去设置") { _, _ ->
                openAccessibilitySettings(context)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 检查欢律App是否已安装
     */
    fun isHeytapHeadsetInstalled(context: Context): Boolean {
        return try {
            context.packageManager.getPackageInfo("com.heytap.headset", 0)
            true
        } catch (e: Exception) {
            Log.w(TAG, "欢律App未安装")
            false
        }
    }

    /**
     * 检查是否为OPPO系列设备（OPPO、一加、realme）
     */
    fun isOppoSeriesDevice(): Boolean {
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        val brand = android.os.Build.BRAND.lowercase()

        val oppoSeries = listOf("oppo", "oneplus", "realme")
        val isOppoDevice = oppoSeries.any {
            manufacturer.contains(it) || brand.contains(it)
        }

        Log.d(TAG, "设备检测: manufacturer=$manufacturer, brand=$brand, isOppoSeries=$isOppoDevice")
        return isOppoDevice
    }

    /**
     * 检查设备是否支持EQ自动化（有欢律App或是OPPO系列设备）
     */
    fun isEQAutomationSupported(context: Context): Boolean {
        val hasHeytapApp = isHeytapHeadsetInstalled(context)
        val isOppoSeries = isOppoSeriesDevice()

        Log.d(TAG, "EQ自动化支持检查: hasHeytapApp=$hasHeytapApp, isOppoSeries=$isOppoSeries")
        return hasHeytapApp || isOppoSeries
    }
}
