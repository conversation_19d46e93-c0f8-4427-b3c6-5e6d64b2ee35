package cn.ykload.seeq

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.ykload.seeq.ui.theme.SeeqTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            SeeqTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    MainScreen(
                        modifier = Modifier.padding(innerPadding)
                    )
                }
            }
        }
    }
}

@Composable
fun MainScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    var isPermissionGranted by remember { mutableStateOf(false) }
    var isHeytapInstalled by remember { mutableStateOf(false) }
    var statusMessage by remember { mutableStateOf("") }
    var showEQSettings by remember { mutableStateOf(false) }

    // EQ参数状态
    var eqGains by remember {
        mutableStateOf(
            SeqAccessibilityService.instance?.getCurrentEQGains() ?: arrayOf(3, 4, -1, 0, -5, -6)
        )
    }

    // 检查权限状态和App安装状态
    LaunchedEffect(Unit) {
        isPermissionGranted = AccessibilityPermissionHelper.isAccessibilityServiceEnabled(context)
        isHeytapInstalled = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(context)

        statusMessage = when {
            !isHeytapInstalled -> "欢律App未安装"
            !isPermissionGranted -> context.getString(R.string.permission_required)
            else -> context.getString(R.string.permission_granted)
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.app_name),
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = statusMessage,
                    style = MaterialTheme.typography.bodyLarge,
                    color = when {
                        !isHeytapInstalled -> MaterialTheme.colorScheme.error
                        isPermissionGranted -> MaterialTheme.colorScheme.primary
                        else -> MaterialTheme.colorScheme.error
                    }
                )

                // 显示自动化操作步骤
                if (SeqAccessibilityService.isAutomationRunning) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = getStepDescription(SeqAccessibilityService.instance?.getCurrentStep() ?: 0),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }

        if (!isPermissionGranted) {
            Button(
                onClick = {
                    AccessibilityPermissionHelper.openAccessibilitySettings(context)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            ) {
                Text(stringResource(R.string.check_permission))
            }
        }

        // EQ设置按钮
        Button(
            onClick = { showEQSettings = !showEQSettings },
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp)
        ) {
            Text(if (showEQSettings) "隐藏EQ设置" else "自定义EQ设置")
        }

        // EQ设置面板
        if (showEQSettings) {
            EQSettingsPanel(
                eqGains = eqGains,
                onEQGainsChanged = { newGains ->
                    eqGains = newGains
                    SeqAccessibilityService.instance?.setCustomEQGains(newGains)
                },
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        Button(
            onClick = {
                when {
                    !isHeytapInstalled -> {
                        // 可以在这里添加引导用户安装欢律App的逻辑
                        statusMessage = "请先安装欢律App"
                    }
                    !isPermissionGranted -> {
                        AccessibilityPermissionHelper.showPermissionGuideDialog(context)
                    }
                    else -> {
                        SeqAccessibilityService.instance?.startAutomation()
                        statusMessage = context.getString(R.string.automation_running)
                    }
                }
            },
            enabled = isHeytapInstalled && isPermissionGranted,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(stringResource(R.string.start_automation))
        }

        // 刷新权限状态按钮
        TextButton(
            onClick = {
                isPermissionGranted = AccessibilityPermissionHelper.isAccessibilityServiceEnabled(context)
                isHeytapInstalled = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(context)

                statusMessage = when {
                    !isHeytapInstalled -> "欢律App未安装"
                    !isPermissionGranted -> context.getString(R.string.permission_required)
                    else -> context.getString(R.string.permission_granted)
                }
            },
            modifier = Modifier.padding(top = 16.dp)
        ) {
            Text("刷新状态")
        }
    }
}

@Composable
fun EQSettingsPanel(
    eqGains: Array<Int>,
    onEQGainsChanged: (Array<Int>) -> Unit,
    modifier: Modifier = Modifier
) {
    val frequencies = arrayOf("62Hz", "250Hz", "1kHz", "4kHz", "8kHz", "16kHz")

    Card(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "EQ均衡器设置",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 预设按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        val defaultGains = arrayOf(3, 4, -1, 0, -5, -6)
                        onEQGainsChanged(defaultGains)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("默认", style = MaterialTheme.typography.bodySmall)
                }

                Button(
                    onClick = {
                        val flatGains = arrayOf(0, 0, 0, 0, 0, 0)
                        onEQGainsChanged(flatGains)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("平坦", style = MaterialTheme.typography.bodySmall)
                }

                Button(
                    onClick = {
                        val bassBoostGains = arrayOf(6, 4, 2, 0, -2, -4)
                        onEQGainsChanged(bassBoostGains)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("低音", style = MaterialTheme.typography.bodySmall)
                }
            }

            // 测试按钮行
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        val testGains = arrayOf(-6, -2, 0, 2, 4, 6)
                        onEQGainsChanged(testGains)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("测试", style = MaterialTheme.typography.bodySmall)
                }

                Button(
                    onClick = {
                        val extremeGains = arrayOf(-6, -4, -2, 2, 4, 6)
                        onEQGainsChanged(extremeGains)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("极值", style = MaterialTheme.typography.bodySmall)
                }
            }

            // 显示当前EQ值
            Text(
                text = "当前EQ: ${eqGains.contentToString()}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // 各频段调节
            frequencies.forEachIndexed { index, frequency ->
                EQBandSlider(
                    frequency = frequency,
                    gain = eqGains[index],
                    onGainChanged = { newGain ->
                        val newGains = eqGains.copyOf()
                        newGains[index] = newGain
                        onEQGainsChanged(newGains)
                    }
                )
            }
        }
    }
}

@Composable
fun EQBandSlider(
    frequency: String,
    gain: Int,
    onGainChanged: (Int) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = frequency,
            modifier = Modifier.width(60.dp),
            style = MaterialTheme.typography.bodyMedium
        )

        Slider(
            value = gain.toFloat(),
            onValueChange = { newValue ->
                // 确保值是整数
                val roundedValue = kotlin.math.round(newValue).toInt()
                onGainChanged(roundedValue)
            },
            valueRange = -6f..6f,
            steps = 11, // 从-6到+6共13个值，中间有11个分割点：-5,-4,-3,-2,-1,0,+1,+2,+3,+4,+5
            modifier = Modifier.weight(1f)
        )

        Text(
            text = "${if (gain > 0) "+" else ""}${gain}dB",
            modifier = Modifier.width(50.dp),
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * 获取步骤描述
 */
fun getStepDescription(step: Int): String {
    return when (step) {
        0 -> "准备开始..."
        1 -> "正在打开欢律App..."
        2 -> "等待噪声控制界面..."
        3 -> "查找大师调音..."
        4 -> "选择Seeq配置..."
        5 -> "正在调节EQ均衡器..."
        6 -> "操作完成！"
        else -> "未知步骤"
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    SeeqTheme {
        MainScreen()
    }
}