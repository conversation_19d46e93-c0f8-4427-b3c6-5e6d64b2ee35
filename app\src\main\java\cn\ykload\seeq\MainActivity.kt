package cn.ykload.seeq

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.webkit.*
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import java.util.regex.Pattern

class MainActivity : AppCompatActivity(), AutomationCallback {

    companion object {
        private const val TAG = "MainActivity"
        private const val SEEQ_URL = "https://seeq.ykload.com"
        private val EQ_PATTERN = Pattern.compile("\\[Seeq\\]\\{([^}]+)\\}")
    }

    private lateinit var webView: WebView

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建WebView
        webView = WebView(this)
        setContentView(webView)

        // 配置WebView
        setupWebView()

        // 设置自动化完成回调
        SeqAccessibilityService.instance?.setAutomationCallback(this)

        // 加载网页
        webView.loadUrl(SEEQ_URL)

        Log.d(TAG, "WebView已创建并开始加载: $SEEQ_URL")
    }

    /**
     * 配置WebView设置
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        val webSettings = webView.settings

        // 启用JavaScript
        webSettings.javaScriptEnabled = true

        // 启用DOM存储
        webSettings.domStorageEnabled = true

        // 启用数据库存储
        webSettings.databaseEnabled = true


        // 设置缓存模式
        webSettings.cacheMode = WebSettings.LOAD_DEFAULT

        // 允许文件访问
        webSettings.allowFileAccess = true

        // 允许内容访问
        webSettings.allowContentAccess = true

        // 支持缩放
        webSettings.setSupportZoom(true)
        webSettings.builtInZoomControls = true
        webSettings.displayZoomControls = false

        // 设置用户代理
        webSettings.userAgentString = webSettings.userAgentString + " SeeqApp/1.0"

        // 设置WebViewClient
        webView.webViewClient = SeeqWebViewClient()

        // 设置WebChromeClient用于监听控制台消息
        webView.webChromeClient = SeeqWebChromeClient()

        Log.d(TAG, "WebView配置完成")
    }

    /**
     * 自定义WebViewClient
     */
    private inner class SeeqWebViewClient : WebViewClient() {

        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            // 允许在当前WebView中加载所有URL
            return false
        }

        override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
            super.onPageStarted(view, url, favicon)
            Log.d(TAG, "开始加载页面: $url")
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            Log.d(TAG, "页面加载完成: $url")

            // 注入JavaScript代码来监听控制台输出
            injectConsoleMonitor()
        }

        override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
            super.onReceivedError(view, request, error)
            Log.e(TAG, "WebView加载错误: ${error?.description}")
        }
    }

    /**
     * 自定义WebChromeClient用于监听控制台消息
     */
    private inner class SeeqWebChromeClient : WebChromeClient() {

        override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
            consoleMessage?.let { message ->
                val logMessage = "[WebView Console] ${message.message()}"
                Log.d(TAG, logMessage)

                // 检查是否包含Seeq EQ参数
                checkForEQParameters(message.message())
            }
            return true
        }

        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            if (newProgress == 100) {
                Log.d(TAG, "页面加载进度: 100%")
            }
        }
    }

    /**
     * 注入JavaScript代码来监听控制台输出
     */
    private fun injectConsoleMonitor() {
        val jsCode = """
            (function() {
                // 保存原始的console.log方法
                var originalLog = console.log;
                var originalWarn = console.warn;
                var originalError = console.error;
                var originalInfo = console.info;

                // 重写console.log方法
                console.log = function() {
                    originalLog.apply(console, arguments);
                    // 通过Android接口发送消息
                    if (window.Android) {
                        window.Android.onConsoleMessage('log', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.warn = function() {
                    originalWarn.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('warn', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.error = function() {
                    originalError.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('error', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.info = function() {
                    originalInfo.apply(console, arguments);
                    if (window.Android) {
                        window.Android.onConsoleMessage('info', Array.prototype.slice.call(arguments).join(' '));
                    }
                };

                console.log('Seeq控制台监听已启动');
            })();
        """.trimIndent()

        webView.evaluateJavascript(jsCode) { result ->
            Log.d(TAG, "控制台监听脚本注入完成: $result")
        }

        // 添加JavaScript接口
        webView.addJavascriptInterface(JavaScriptInterface(), "Android")
    }

    /**
     * JavaScript接口类
     */
    private inner class JavaScriptInterface {

        @JavascriptInterface
        fun onConsoleMessage(level: String, message: String) {
            Log.d(TAG, "[$level] $message")

            // 在主线程中处理控制台消息
            runOnUiThread {
                checkForEQParameters(message)
            }
        }
    }

    /**
     * 检查控制台消息中是否包含EQ参数
     */
    private fun checkForEQParameters(message: String) {
        val matcher = EQ_PATTERN.matcher(message)
        if (matcher.find()) {
            val eqParams = matcher.group(1)
            Log.d(TAG, "检测到EQ参数: $eqParams")

            // 解析EQ参数
            val params = parseEQParameters(eqParams)
            if (params != null) {
                Log.d(TAG, "解析的EQ参数: ${params.contentToString()}")

                // 触发自动化操作
                triggerAutomation(params)
            } else {
                Log.w(TAG, "EQ参数解析失败: $eqParams")
            }
        }
    }

    /**
     * 解析EQ参数字符串
     * 格式: +1,+1,+4,+5,+1,+4
     */
    private fun parseEQParameters(paramString: String): Array<Int>? {
        return try {
            val params = paramString.split(",")
            if (params.size != 6) {
                Log.w(TAG, "EQ参数数量不正确，期望6个，实际${params.size}个")
                return null
            }

            val result = Array(6) { 0 }
            for (i in params.indices) {
                val param = params[i].trim()
                result[i] = when {
                    param.startsWith("+") -> param.substring(1).toInt()
                    else -> param.toInt()
                }

                // 验证参数范围 (-6 到 +6)
                if (result[i] < -6 || result[i] > 6) {
                    Log.w(TAG, "EQ参数超出范围: ${result[i]}")
                    return null
                }
            }

            result
        } catch (e: Exception) {
            Log.e(TAG, "解析EQ参数时出错: $paramString", e)
            null
        }
    }

    /**
     * 触发自动化操作
     */
    private fun triggerAutomation(eqParams: Array<Int>) {
        Log.d(TAG, "准备触发自动化操作，EQ参数: ${eqParams.contentToString()}")

        // 检查无障碍权限
        if (!AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
            Log.w(TAG, "无障碍权限未授予，显示权限请求弹窗")
            showPermissionDialog(eqParams)
            return
        }

        // 检查设备是否支持EQ自动化
        if (!AccessibilityPermissionHelper.isEQAutomationSupported(this)) {
            Log.w(TAG, "设备不支持EQ自动化")
            val deviceInfo = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
            Toast.makeText(this, "当前设备($deviceInfo)不支持EQ自动化功能", Toast.LENGTH_LONG).show()
            return
        }

        // 显示使用的自动化方式
        val hasHeytapApp = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)
        val automationMethod = if (hasHeytapApp) {
            "使用欢律App进行自动化"
        } else {
            "使用系统蓝牙设置进行自动化（OPPO系列设备）"
        }
        Log.d(TAG, automationMethod)

        // 设置EQ参数并启动自动化
        SeqAccessibilityService.instance?.setCustomEQGains(eqParams)
        SeqAccessibilityService.instance?.startAutomation()

        Toast.makeText(this, "开始自动导入EQ设置", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "自动化操作已启动")
    }

    /**
     * 显示权限请求弹窗
     */
    private fun showPermissionDialog(eqParams: Array<Int>) {
        AlertDialog.Builder(this)
            .setTitle("需要无障碍权限")
            .setMessage("需要无障碍权限以自动导入EQ设置。\n\n若Seeq后台被杀，则需要重新设置权限。")
            .setCancelable(false)
            .setPositiveButton("去设置") { _, _ ->
                AccessibilityPermissionHelper.openAccessibilitySettings(this)
            }
            .setNegativeButton("我已设置") { dialog, _ ->
                // 重新检查权限
                if (AccessibilityPermissionHelper.isAccessibilityServiceEnabled(this)) {
                    dialog.dismiss()
                    // 权限已授予，启动自动化
                    triggerAutomation(eqParams)
                } else {
                    // 权限仍未授予，不关闭弹窗
                    Toast.makeText(this, "权限尚未授予，请先去设置中开启", Toast.LENGTH_LONG).show()
                }
            }
            .show()
    }

    @SuppressLint("GestureBackNavigation")
    override fun onResume() {
        super.onResume()
        // 确保回调设置正确（防止服务重启后回调丢失）
        SeqAccessibilityService.instance?.setAutomationCallback(this)
    }

    @SuppressLint("GestureBackNavigation")
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }

    override fun onDestroy() {
        // 清除回调
        SeqAccessibilityService.instance?.setAutomationCallback(null)
        webView.destroy()
        super.onDestroy()
    }

    /**
     * 自动化完成回调实现
     */
    override fun onAutomationCompleted(eqParams: Array<Int>) {
        runOnUiThread {
            showSuccessMessages(eqParams)
        }
    }

    /**
     * 显示成功消息气泡
     */
    private fun showSuccessMessages(eqParams: Array<Int>) {
        // 格式化EQ参数为字符串
        val eqString = eqParams.joinToString(",") { value ->
            if (value > 0) "+$value" else value.toString()
        }

        // 第一个消息：EQ应用成功
        val firstMessage = "EQ应用成功：{$eqString}"
        Toast.makeText(this, firstMessage, Toast.LENGTH_LONG).show()

        Log.d(TAG, "显示第一个成功消息: $firstMessage")

        // 延迟显示第二个消息
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            val secondMessage = "若以上EQ与目前调整EQ的不一致，请切回Seeq再进行一次导入。"
            Toast.makeText(this, secondMessage, Toast.LENGTH_LONG).show()

            Log.d(TAG, "显示第二个成功消息: $secondMessage")
        }, 3500) // 3.5秒后显示第二个消息，确保第一个消息显示完毕
    }
}

