package cn.ykload.seeq

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Intent
import android.graphics.Path
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo

/**
 * EQ坐标信息数据类
 */
data class EQCoordinates(
    val bandCenterX: Array<Float>,  // 6个频段的X坐标中心点
    val eqTopY: Float,              // EQ控制区域顶部Y坐标
    val eqBottomY: Float,           // EQ控制区域底部Y坐标
    val eqHeight: Float             // EQ控制区域高度
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as EQCoordinates
        if (!bandCenterX.contentEquals(other.bandCenterX)) return false
        if (eqTopY != other.eqTopY) return false
        if (eqBottomY != other.eqBottomY) return false
        if (eqHeight != other.eqHeight) return false
        return true
    }

    override fun hashCode(): Int {
        var result = bandCenterX.contentHashCode()
        result = 31 * result + eqTopY.hashCode()
        result = 31 * result + eqBottomY.hashCode()
        result = 31 * result + eqHeight.hashCode()
        return result
    }
}

class SeqAccessibilityService : AccessibilityService() {

    companion object {
        private const val TAG = "SeqAccessibilityService"
        const val ACTION_START_AUTOMATION = "cn.ykload.seeq.START_AUTOMATION"

        // 自动化操作状态
        var isAutomationRunning = false
        var instance: SeqAccessibilityService? = null
    }

    private val handler = Handler(Looper.getMainLooper())
    private var currentStep = 0

    // 优化后的速度配置
    private val DETECTION_INTERVAL = 100L      // 检测间隔：0.1秒
    private val CLICK_DELAY = 100L             // 点击间隔：0.1秒（调整后）
    private val STEP_DELAY = 200L             // 步骤间延迟：0.2秒
    private val MODAL_WAIT_DELAY = 800L       // 模态框等待：0.8秒（增加等待时间）
    private val EQ_MODAL_FORCE_WAIT = 500L    // EQ模态框强制等待：0.5秒（确保完全弹出）

    // 自定义EQ参数 (对应62Hz, 250Hz, 1kHz, 4kHz, 8kHz, 16kHz)
    private var customEQGains = arrayOf(3, 4, -1, 0, -5, -6) // 默认值

    // EQ调节重复执行控制
    private var eqAdjustmentCount = 0  // EQ调节执行次数
    private val MAX_EQ_ADJUSTMENTS = 2 // 最大执行次数（第一次 + 重复一次）

    override fun onServiceConnected() {
        super.onServiceConnected()
        instance = this
        Log.d(TAG, "无障碍服务已连接")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (!isAutomationRunning) return

        event?.let {
            Log.d(TAG, "收到事件: ${it.eventType}, 包名: ${it.packageName}")

            when (it.packageName) {
                "com.heytap.headset" -> {
                    // 在欢律App中的操作 - 使用智能检测
                    performSmartDetection()
                }
                "com.android.settings" -> {
                    // 在系统设置中的操作（OPPO系列设备的蓝牙设置）
                    if (AccessibilityPermissionHelper.isOppoSeriesDevice()) {
                        performOppoSmartDetection()
                    }
                }
                else -> {
                    // 其他包名，可能是OPPO系列设备的内置音频设置
                    if (AccessibilityPermissionHelper.isOppoSeriesDevice()) {
                        // 检查是否在音频相关的界面
                        val rootNode = rootInActiveWindow
                        if (rootNode != null) {
                            val hasAudioKeywords = findNodeByText(rootNode, "大师调音") != null ||
                                                 findNodeByText(rootNode, "音效") != null ||
                                                 findNodeByText(rootNode, "Seeq") != null ||
                                                 findNodeByText(rootNode, "均衡器") != null

                            if (hasAudioKeywords) {
                                Log.d(TAG, "在OPPO音频设置界面中，使用OPPO智能检测")
                                performOppoSmartDetection()
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onInterrupt() {
        Log.d(TAG, "无障碍服务被中断")
        isAutomationRunning = false
    }

    override fun onUnbind(intent: Intent?): Boolean {
        instance = null
        isAutomationRunning = false
        return super.onUnbind(intent)
    }

    /**
     * 获取当前步骤
     */
    fun getCurrentStep(): Int {
        return currentStep
    }

    /**
     * 获取当前步骤描述
     */
    fun getCurrentStepDescription(): String {
        val hasHeytapApp = AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)

        return if (hasHeytapApp) {
            // 欢律App的步骤描述
            when (currentStep) {
                0 -> "准备开始..."
                1 -> "正在打开欢律App..."
                2 -> "等待噪声控制界面..."
                3 -> "查找大师调音..."
                4 -> "选择Seeq配置..."
                5 -> "正在调节EQ均衡器..."
                6 -> "操作完成！"
                else -> "未知步骤"
            }
        } else {
            // OPPO系列设备的步骤描述
            when (currentStep) {
                0 -> "准备开始..."
                1 -> "正在打开蓝牙设置..."
                2 -> "查找使用中的蓝牙设备..."
                3 -> "查找耳机功能..."
                4 -> "查找大师调音..."
                5 -> "选择Seeq配置..."
                6 -> "正在调节EQ均衡器..."
                7 -> "操作完成！"
                else -> "未知步骤"
            }
        }
    }

    /**
     * 开始自动化操作
     */
    fun startAutomation() {
        if (isAutomationRunning) {
            Log.d(TAG, "自动化操作已在运行中")
            return
        }
        
        isAutomationRunning = true
        currentStep = 0
        eqAdjustmentCount = 0  // 重置EQ调节计数器
        Log.d(TAG, "开始智能自动化操作")
        Log.d(TAG, "智能检测配置:")
        Log.d(TAG, "  检测间隔: ${DETECTION_INTERVAL}ms (${DETECTION_INTERVAL/1000f}秒)")
        Log.d(TAG, "  点击间隔: ${CLICK_DELAY}ms (${CLICK_DELAY/1000f}秒)")
        Log.d(TAG, "  步骤延迟: ${STEP_DELAY}ms (${STEP_DELAY/1000f}秒)")
        Log.d(TAG, "  模态框等待: ${MODAL_WAIT_DELAY}ms (${MODAL_WAIT_DELAY/1000f}秒)")
        Log.d(TAG, "  EQ模态框强制等待: ${EQ_MODAL_FORCE_WAIT}ms (${EQ_MODAL_FORCE_WAIT/1000f}秒) - 确保完全弹出")
        Log.d(TAG, "  智能检测: 同时检测所有步骤标志，自动跳转到对应步骤")

        // 第一步：根据设备类型选择启动方式
        if (AccessibilityPermissionHelper.isHeytapHeadsetInstalled(this)) {
            Log.d(TAG, "检测到欢律App，使用标准启动方式")
            openHeytapHeadsetApp()
        } else if (AccessibilityPermissionHelper.isOppoSeriesDevice()) {
            Log.d(TAG, "检测到OPPO系列设备，使用蓝牙设置启动方式")
            openBluetoothSettingsForOppo()
        } else {
            Log.e(TAG, "设备不支持EQ自动化")
            isAutomationRunning = false
        }
    }

    /**
     * 设置自定义EQ参数
     * @param gains 6个频段的增益值数组 (对应62Hz, 250Hz, 1kHz, 4kHz, 8kHz, 16kHz)
     */
    fun setCustomEQGains(gains: Array<Int>) {
        if (gains.size == 6) {
            customEQGains = gains
            Log.d(TAG, "设置自定义EQ参数: ${gains.contentToString()}")
        } else {
            Log.e(TAG, "EQ参数数组长度必须为6，当前长度: ${gains.size}")
        }
    }

    /**
     * 获取当前EQ参数
     */
    fun getCurrentEQGains(): Array<Int> {
        return customEQGains
    }

    /**
     * 检查并处理"取消"按钮（通用方法）
     * @return true 如果发现并点击了取消按钮，false 如果没有发现取消按钮
     */
    private fun checkAndHandleCancelButton(rootNode: AccessibilityNodeInfo): Boolean {
        // 使用精确匹配查找"取消"按钮，确保文本完全一致
        val cancelNode = findNodeByExactText(rootNode, "取消")
        if (cancelNode != null) {
            Log.d(TAG, "发现取消按钮（精确匹配），点击取消")
            return safeClickNode(cancelNode)
        }
        return false
    }

    /**
     * 打开欢律App
     */
    private fun openHeytapHeadsetApp() {
        try {
            val intent = packageManager.getLaunchIntentForPackage("com.heytap.headset")
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(intent)
                Log.d(TAG, "正在打开欢律App")
                currentStep = 1

                // 启动智能检测
                handler.postDelayed({
                    Log.d(TAG, "App启动完成，开始智能检测")
                    performSmartDetection()
                }, 1500) // App启动等待时间
            } else {
                Log.e(TAG, "未找到欢律App")
                isAutomationRunning = false
            }
        } catch (e: Exception) {
            Log.e(TAG, "打开欢律App失败", e)
            isAutomationRunning = false
        }
    }

    /**
     * OPPO系列设备：通过蓝牙设置打开内置的耳机功能
     */
    private fun openBluetoothSettingsForOppo() {
        try {
            val intent = Intent(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            Log.d(TAG, "正在打开蓝牙设置（OPPO系列设备）")
            currentStep = 1

            // 启动OPPO专用的智能检测
            handler.postDelayed({
                Log.d(TAG, "蓝牙设置启动完成，开始OPPO专用智能检测")
                performOppoSmartDetection()
            }, 2000) // 蓝牙设置启动等待时间稍长
        } catch (e: Exception) {
            Log.e(TAG, "打开蓝牙设置失败", e)
            isAutomationRunning = false
        }
    }

    /**
     * 等待"噪声控制"出现
     */
    private fun waitForNoiseControl() {
        if (!isAutomationRunning) return

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ waitForNoiseControl() }, STEP_DELAY)
                return
            }

            // 查找"噪声控制"
            val noiseControlNode = findNodeByText(rootNode, "噪声控制")
            if (noiseControlNode != null) {
                Log.d(TAG, "发现噪声控制，开始查找大师调音")
                currentStep = 2
                handler.postDelayed({ findMasterTuning() }, DETECTION_INTERVAL)
            } else {
                // 继续等待
                handler.postDelayed({ waitForNoiseControl() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ waitForNoiseControl() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 查找并点击"大师调音"
     */
    private fun findMasterTuning() {
        if (!isAutomationRunning) return

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ findMasterTuning() }, STEP_DELAY)
                return
            }

            val masterTuningNode = findNodeByText(rootNode, "大师调音")
            if (masterTuningNode != null) {
                Log.d(TAG, "发现大师调音，点击")
                if (safeClickNode(masterTuningNode)) {
                    currentStep = 3
                    handler.postDelayed({ findSeeq() }, STEP_DELAY)
                } else {
                    handler.postDelayed({ findMasterTuning() }, DETECTION_INTERVAL)
                }
            } else {
                // 尝试向下滚动
                Log.d(TAG, "未找到大师调音，尝试向下滚动")
                scrollDown()
                handler.postDelayed({ findMasterTuning() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ findMasterTuning() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 查找并点击"Seeq"
     */
    private fun findSeeq() {
        if (!isAutomationRunning) return

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ findSeeq() }, STEP_DELAY)
                return
            }

            val seeqNode = findNodeByText(rootNode, "Seeq")
            if (seeqNode != null) {
                Log.d(TAG, "发现Seeq，点击")
                if (safeClickNode(seeqNode)) {
                    currentStep = 4
                    Log.d(TAG, "Seeq已选中，等待EQ模态框弹出")
                    // 等待EQ模态框弹出并完全加载后开始调节
                    handler.postDelayed({ waitForEQModal() }, MODAL_WAIT_DELAY)
                } else {
                    handler.postDelayed({ findSeeq() }, DETECTION_INTERVAL)
                }
            } else {
                // 继续等待
                handler.postDelayed({ findSeeq() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ findSeeq() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 智能检测 - 同时检测所有步骤的标志
     */
    private fun performSmartDetection() {
        if (!isAutomationRunning) return

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ performSmartDetection() }, STEP_DELAY)
                return
            }

            // 检测当前界面状态
            val detectedStep = detectCurrentStep(rootNode)

            if (detectedStep != -1) {
                Log.d(TAG, "智能检测到步骤 $detectedStep，从此步骤开始执行")
                currentStep = detectedStep
                executeStepAction(detectedStep)
            } else {
                // 没有检测到任何已知步骤，继续检测
                handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * OPPO专用智能检测 - 检测蓝牙设置中的特定元素
     */
    private fun performOppoSmartDetection() {
        if (!isAutomationRunning) return

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ performOppoSmartDetection() }, STEP_DELAY)
                return
            }

            // 检测OPPO蓝牙设置中的步骤
            val detectedStep = detectOppoCurrentStep(rootNode)

            if (detectedStep != -1) {
                Log.d(TAG, "OPPO智能检测到步骤 $detectedStep，从此步骤开始执行")
                currentStep = detectedStep
                executeOppoStepAction(detectedStep)
            } else {
                // 没有检测到任何已知步骤，继续检测
                handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 检测当前步骤
     * @return 检测到的步骤编号，-1表示未检测到
     */
    private fun detectCurrentStep(rootNode: AccessibilityNodeInfo): Int {
        // 定义各步骤的标志关键词
        val stepIndicators = mapOf(
            1 to arrayOf("噪声控制"),                    // 步骤1：等待噪声控制界面
            2 to arrayOf("大师调音", "音效"),             // 步骤2：查找大师调音
            3 to arrayOf("Seeq", "均衡器"),              // 步骤3：查找Seeq选项
            4 to arrayOf("关闭", "重命名", "62", "250", "+6 dB", "-6 dB"), // 步骤4：EQ模态框
            5 to arrayOf("62", "250", "1k", "4k", "8k", "16k") // 步骤5：EQ调节界面
        )

        // 按优先级检测（从高到低）
        val priorityOrder = arrayOf(5, 4, 3, 2, 1)

        for (step in priorityOrder) {
            val indicators = stepIndicators[step] ?: continue
            var foundCount = 0

            for (indicator in indicators) {
                if (findNodeByText(rootNode, indicator) != null) {
                    foundCount++
                }
            }

            // 根据不同步骤设置不同的检测阈值
            val requiredCount = when (step) {
                1 -> 1  // 噪声控制界面：找到1个即可
                2 -> 1  // 大师调音界面：找到1个即可
                3 -> 1  // Seeq选择界面：找到1个即可
                4 -> 3  // EQ模态框：至少找到3个指示器
                5 -> 4  // EQ调节界面：至少找到4个频段标签
                else -> 1
            }

            if (foundCount >= requiredCount) {
                Log.d(TAG, "检测到步骤 $step，找到 $foundCount 个指示器: ${indicators.take(foundCount).joinToString()}")

                // 特殊处理：如果检测到EQ界面（步骤4或5），需要确保模态框完全弹出
                if (step == 4 || step == 5) {
                    Log.d(TAG, "检测到EQ界面，需要等待模态框完全弹出")
                }

                return step
            }
        }

        return -1 // 未检测到任何步骤
    }

    /**
     * 检测OPPO设备蓝牙设置中的当前步骤
     * @return 检测到的步骤编号，-1表示未检测到
     */
    private fun detectOppoCurrentStep(rootNode: AccessibilityNodeInfo): Int {
        // 定义OPPO蓝牙设置中各步骤的标志关键词
        val oppoStepIndicators = mapOf(
            1 to arrayOf("使用中", "已连接"),              // 步骤1：查找使用中的蓝牙设备
            2 to arrayOf("耳机功能", "音频设置"),          // 步骤2：查找耳机功能入口
            3 to arrayOf("大师调音", "音效"),             // 步骤3：进入音效设置后查找大师调音
            4 to arrayOf("Seeq", "均衡器"),              // 步骤4：查找Seeq选项
            5 to arrayOf("关闭", "重命名", "62", "250", "+6 dB", "-6 dB"), // 步骤5：EQ模态框
            6 to arrayOf("62", "250", "1k", "4k", "8k", "16k") // 步骤6：EQ调节界面
        )

        // 按优先级检测（从高到低）
        val priorityOrder = arrayOf(6, 5, 4, 3, 2, 1)

        for (step in priorityOrder) {
            val indicators = oppoStepIndicators[step] ?: continue
            var foundCount = 0

            for (indicator in indicators) {
                if (findNodeByText(rootNode, indicator) != null) {
                    foundCount++
                }
            }

            // 根据不同步骤设置不同的检测阈值
            val requiredCount = when (step) {
                1 -> 1  // 蓝牙设备列表：找到1个即可
                2 -> 1  // 耳机功能界面：找到1个即可
                3 -> 1  // 大师调音界面：找到1个即可
                4 -> 1  // Seeq选择界面：找到1个即可
                5 -> 3  // EQ模态框：至少找到3个指示器
                6 -> 4  // EQ调节界面：至少找到4个频段标签
                else -> 1
            }

            if (foundCount >= requiredCount) {
                Log.d(TAG, "OPPO检测到步骤 $step，找到 $foundCount 个指示器: ${indicators.take(foundCount).joinToString()}")
                return step
            }
        }

        return -1 // 未检测到任何步骤
    }

    /**
     * 执行对应步骤的操作
     */
    private fun executeStepAction(step: Int) {
        when (step) {
            1 -> {
                Log.d(TAG, "执行步骤1：处理噪声控制界面")
                findAndClickNoiseControl()
            }
            2 -> {
                Log.d(TAG, "执行步骤2：查找并点击大师调音")
                findAndClickMasterTuning()
            }
            3 -> {
                Log.d(TAG, "执行步骤3：查找并点击Seeq")
                findAndClickSeeq()
            }
            4 -> {
                Log.d(TAG, "执行步骤4：检测到EQ模态框，强制等待${EQ_MODAL_FORCE_WAIT}ms确保完全弹出")
                handler.postDelayed({
                    Log.d(TAG, "EQ模态框强制等待完成，开始稳定性检查")
                    waitForEQModalStable()
                }, EQ_MODAL_FORCE_WAIT)
            }
            5 -> {
                Log.d(TAG, "执行步骤5：检测到EQ调节界面，强制等待${EQ_MODAL_FORCE_WAIT}ms确保模态框完全弹出")
                handler.postDelayed({
                    Log.d(TAG, "EQ界面强制等待完成，开始调节")
                    adjustEqualizer()
                }, EQ_MODAL_FORCE_WAIT)
            }
        }
    }

    /**
     * 执行OPPO专用步骤的操作
     */
    private fun executeOppoStepAction(step: Int) {
        when (step) {
            1 -> {
                Log.d(TAG, "执行OPPO步骤1：查找并点击使用中的蓝牙设备")
                findAndClickActiveBluetoothDevice()
            }
            2 -> {
                Log.d(TAG, "执行OPPO步骤2：查找并点击耳机功能")
                findAndClickHeadsetFunction()
            }
            3 -> {
                Log.d(TAG, "执行OPPO步骤3：查找并点击大师调音")
                findAndClickMasterTuning()
            }
            4 -> {
                Log.d(TAG, "执行OPPO步骤4：查找并点击Seeq")
                findAndClickSeeq()
            }
            5 -> {
                Log.d(TAG, "执行OPPO步骤5：检测到EQ模态框，强制等待${EQ_MODAL_FORCE_WAIT}ms确保完全弹出")
                handler.postDelayed({
                    Log.d(TAG, "EQ模态框强制等待完成，开始稳定性检查")
                    waitForEQModalStable()
                }, EQ_MODAL_FORCE_WAIT)
            }
            6 -> {
                Log.d(TAG, "执行OPPO步骤6：检测到EQ调节界面，强制等待${EQ_MODAL_FORCE_WAIT}ms确保模态框完全弹出")
                handler.postDelayed({
                    Log.d(TAG, "EQ界面强制等待完成，开始调节")
                    adjustEqualizer()
                }, EQ_MODAL_FORCE_WAIT)
            }
        }
    }

    /**
     * 步骤1：查找并点击噪声控制
     */
    private fun findAndClickNoiseControl() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            val noiseControlNode = findNodeByText(rootNode, "噪声控制")
            if (noiseControlNode != null) {
                Log.d(TAG, "发现噪声控制，准备进入大师调音")
                currentStep = 2
                handler.postDelayed({ performSmartDetection() }, STEP_DELAY)
            } else {
                handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * OPPO步骤1：查找并点击使用中的蓝牙设备
     */
    private fun findAndClickActiveBluetoothDevice() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 查找包含"使用中"或"已连接"字样的元素
            val activeDeviceNode = findNodeByText(rootNode, "使用中")
                ?: findNodeByText(rootNode, "已连接")

            if (activeDeviceNode != null) {
                Log.d(TAG, "发现使用中的蓝牙设备，点击进入")
                if (safeClickNode(activeDeviceNode)) {
                    currentStep = 2
                    handler.postDelayed({ performOppoSmartDetection() }, STEP_DELAY)
                } else {
                    handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
                }
            } else {
                // 尝试向下滚动寻找
                Log.d(TAG, "未找到使用中的蓝牙设备，尝试滚动")
                scrollDown()
                handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * OPPO步骤2：查找并点击耳机功能
     */
    private fun findAndClickHeadsetFunction() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 查找"耳机功能"或"音频设置"字样的元素
            val headsetFunctionNode = findNodeByText(rootNode, "耳机功能")
                ?: findNodeByText(rootNode, "音频设置")

            if (headsetFunctionNode != null) {
                Log.d(TAG, "发现耳机功能，点击进入")
                if (safeClickNode(headsetFunctionNode)) {
                    currentStep = 3
                    handler.postDelayed({ performOppoSmartDetection() }, STEP_DELAY)
                } else {
                    handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
                }
            } else {
                // 尝试向下滚动寻找
                Log.d(TAG, "未找到耳机功能，尝试滚动")
                scrollDown()
                handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performOppoSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 步骤2：查找并点击大师调音
     */
    private fun findAndClickMasterTuning() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            val masterTuningNode = findNodeByText(rootNode, "大师调音")
            if (masterTuningNode != null) {
                Log.d(TAG, "发现大师调音，点击进入")
                if (safeClickNode(masterTuningNode)) {
                    currentStep = 3
                    handler.postDelayed({ performSmartDetection() }, STEP_DELAY)
                } else {
                    handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
                }
            } else {
                // 尝试向下滚动寻找
                Log.d(TAG, "未找到大师调音，尝试滚动")
                scrollDown()
                handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 步骤3：查找并点击Seeq
     */
    private fun findAndClickSeeq() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            val seeqNode = findNodeByText(rootNode, "Seeq")
            if (seeqNode != null) {
                Log.d(TAG, "发现Seeq，点击选择")
                if (safeClickNode(seeqNode)) {
                    currentStep = 4
                    Log.d(TAG, "Seeq已选中，等待EQ模态框弹出（强制等待${EQ_MODAL_FORCE_WAIT}ms）")
                    handler.postDelayed({ performSmartDetection() }, EQ_MODAL_FORCE_WAIT)
                } else {
                    handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
                }
            } else {
                handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 步骤4：等待EQ模态框稳定
     */
    private fun waitForEQModalStable() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            val modalIndicators = arrayOf("关闭", "重命名", "62", "250", "+6 dB", "-6 dB")
            var foundIndicators = 0

            for (indicator in modalIndicators) {
                if (findNodeByText(rootNode, indicator) != null) {
                    foundIndicators++
                }
            }

            if (foundIndicators >= 4) {
                Log.d(TAG, "EQ模态框已稳定，找到 $foundIndicators 个指示器")
                currentStep = 5
                // 额外等待确保稳定
                handler.postDelayed({
                    Log.d(TAG, "模态框稳定等待完成，开始EQ调节")
                    performSmartDetection()
                }, 200)
            } else {
                Log.d(TAG, "EQ模态框还未稳定，找到 $foundIndicators 个指示器")
                handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ performSmartDetection() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 根据文本查找节点（模糊匹配）
     */
    private fun findNodeByText(rootNode: AccessibilityNodeInfo, text: String): AccessibilityNodeInfo? {
        try {
            // 检查当前节点的文本
            if (rootNode.text?.toString()?.contains(text, ignoreCase = true) == true) {
                return rootNode
            }

            // 检查内容描述
            if (rootNode.contentDescription?.toString()?.contains(text, ignoreCase = true) == true) {
                return rootNode
            }

            // 递归检查子节点
            for (i in 0 until rootNode.childCount) {
                val childNode = rootNode.getChild(i)
                if (childNode != null) {
                    val result = findNodeByText(childNode, text)
                    if (result != null) {
                        return result
                    }
                    childNode.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找节点时出错: $text", e)
        }

        return null
    }

    /**
     * 根据文本精确查找节点（完全匹配）
     * 专门用于查找"取消"按钮，确保文本完全一致
     */
    private fun findNodeByExactText(rootNode: AccessibilityNodeInfo, text: String): AccessibilityNodeInfo? {
        try {
            // 检查当前节点的文本（完全匹配）
            if (rootNode.text?.toString()?.equals(text, ignoreCase = false) == true) {
                return rootNode
            }

            // 检查内容描述（完全匹配）
            if (rootNode.contentDescription?.toString()?.equals(text, ignoreCase = false) == true) {
                return rootNode
            }

            // 递归检查子节点
            for (i in 0 until rootNode.childCount) {
                val childNode = rootNode.getChild(i)
                if (childNode != null) {
                    val result = findNodeByExactText(childNode, text)
                    if (result != null) {
                        return result
                    }
                    childNode.recycle()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "精确查找节点时出错: $text", e)
        }

        return null
    }

    /**
     * 向下滚动
     */
    private fun scrollDown() {
        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 尝试在可滚动的视图上执行滚动
            val scrollableNode = findScrollableNode(rootNode)
            if (scrollableNode != null) {
                scrollableNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
            } else {
                // 使用手势滚动
                performSwipeGesture(500f, 800f, 500f, 400f)
            }
        }
    }

    /**
     * 查找可滚动的节点
     */
    private fun findScrollableNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        if (node.isScrollable) {
            return node
        }
        
        for (i in 0 until node.childCount) {
            val childNode = node.getChild(i)
            if (childNode != null) {
                val result = findScrollableNode(childNode)
                if (result != null) {
                    return result
                }
            }
        }
        
        return null
    }

    /**
     * 执行滑动手势
     */
    private fun performSwipeGesture(startX: Float, startY: Float, endX: Float, endY: Float) {
        try {
            val path = Path()
            path.moveTo(startX, startY)
            path.lineTo(endX, endY)

            val gestureBuilder = GestureDescription.Builder()
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 500)
            gestureBuilder.addStroke(strokeDescription)

            val gesture = gestureBuilder.build()
            dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Log.d(TAG, "滑动手势完成")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "滑动手势被取消")
                }
            }, null)
        } catch (e: Exception) {
            Log.e(TAG, "执行滑动手势失败", e)
        }
    }

    /**
     * 安全点击节点
     */
    private fun safeClickNode(node: AccessibilityNodeInfo): Boolean {
        return try {
            if (node.isClickable) {
                node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            } else {
                // 如果节点不可点击，尝试点击父节点
                var parent = node.parent
                while (parent != null) {
                    if (parent.isClickable) {
                        return parent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
                    }
                    parent = parent.parent
                }
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "点击节点失败", e)
            false
        }
    }

    /**
     * 打印节点树结构（用于调试）
     */
    private fun printNodeTree(node: AccessibilityNodeInfo?, depth: Int = 0) {
        if (node == null) return

        val indent = "  ".repeat(depth)
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""

        Log.d(TAG, "${indent}Node: $className, text: '$text', desc: '$contentDesc', clickable: ${node.isClickable}")

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                printNodeTree(child, depth + 1)
                child.recycle()
            }
        }
    }

    /**
     * 分析EQ界面布局（详细版本）
     */
    private fun analyzeEQLayout(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "=================== EQ界面布局分析开始 ===================")

        // 1. 基本屏幕信息
        val screenWidth = resources.displayMetrics.widthPixels
        val screenHeight = resources.displayMetrics.heightPixels
        Log.d(TAG, "屏幕尺寸: ${screenWidth}x${screenHeight}")

        // 2. 查找并分析频段标签
        val frequencies = arrayOf("62", "250", "1k", "4k", "8k", "16k")
        Log.d(TAG, "--- 频段标签坐标分析 ---")
        for (i in frequencies.indices) {
            val freqNode = findNodeByText(rootNode, frequencies[i])
            if (freqNode != null) {
                val bounds = Rect()
                freqNode.getBoundsInScreen(bounds)
                Log.d(TAG, "频段 ${frequencies[i]}: 中心X=${bounds.centerX()}, 中心Y=${bounds.centerY()}, 边界=$bounds")
            } else {
                Log.w(TAG, "未找到频段标签: ${frequencies[i]}")
            }
        }

        // 3. 查找并分析增益标签
        val gainLabels = arrayOf("+6 dB", "+3 dB", "0 dB", "-3 dB", "-6 dB")
        Log.d(TAG, "--- 增益标签坐标分析 ---")
        for (label in gainLabels) {
            val gainNode = findNodeByText(rootNode, label)
            if (gainNode != null) {
                val bounds = Rect()
                gainNode.getBoundsInScreen(bounds)
                Log.d(TAG, "增益 $label: 中心X=${bounds.centerX()}, 中心Y=${bounds.centerY()}, 边界=$bounds")
            } else {
                Log.d(TAG, "未找到增益标签: $label")
            }
        }

        // 4. 查找模态框边界
        val modal = findEQModal(rootNode)
        if (modal != null) {
            val modalBounds = Rect()
            modal.getBoundsInScreen(modalBounds)
            Log.d(TAG, "--- 模态框信息 ---")
            Log.d(TAG, "模态框边界: $modalBounds")
            Log.d(TAG, "模态框高度: ${modalBounds.height()}, 宽度: ${modalBounds.width()}")
        }

        // 5. 查找EQ控制组件
        Log.d(TAG, "--- EQ控制组件分析 ---")
        findAndAnalyzeEQComponents(rootNode)

        // 6. 分析当前EQ设置（如果可能）
        Log.d(TAG, "--- 当前EQ设置分析 ---")
        analyzeCurrentEQSettings(rootNode)

        // 7. 尝试检测当前EQ控制点位置
        Log.d(TAG, "--- 当前EQ控制点位置检测 ---")
        detectCurrentEQControlPoints(rootNode)

        Log.d(TAG, "=================== EQ界面布局分析结束 ===================")

        // 执行设备兼容性检测
        performCompatibilityCheck()
    }

    /**
     * 设备兼容性检测
     */
    private fun performCompatibilityCheck() {
        Log.d(TAG, "=================== 设备兼容性检测 ===================")

        // 基本设备信息
        val manufacturer = android.os.Build.MANUFACTURER
        val model = android.os.Build.MODEL
        val androidVersion = android.os.Build.VERSION.RELEASE
        val sdkVersion = android.os.Build.VERSION.SDK_INT

        Log.d(TAG, "设备信息:")
        Log.d(TAG, "  制造商: $manufacturer")
        Log.d(TAG, "  型号: $model")
        Log.d(TAG, "  Android版本: $androidVersion (API $sdkVersion)")

        // 屏幕信息
        val metrics = resources.displayMetrics
        Log.d(TAG, "屏幕信息:")
        Log.d(TAG, "  分辨率: ${metrics.widthPixels}x${metrics.heightPixels}")
        Log.d(TAG, "  密度: ${metrics.density}x (${metrics.densityDpi} DPI)")
        Log.d(TAG, "  尺寸分类: ${getScreenSizeCategory(metrics)}")

        // 已知兼容设备列表（可以根据测试结果扩展）
        val knownCompatibleDevices = listOf(
            "您当前的设备" // 可以添加具体的设备型号
        )

        val deviceSignature = "$manufacturer $model"
        val isKnownCompatible = knownCompatibleDevices.any {
            deviceSignature.contains(it, ignoreCase = true)
        }

        if (isKnownCompatible) {
            Log.d(TAG, "✅ 设备已验证兼容")
        } else {
            Log.d(TAG, "⚠️ 设备未经验证，建议进行测试")
        }

        Log.d(TAG, "=================== 兼容性检测结束 ===================")
    }

    /**
     * 获取屏幕尺寸分类
     */
    private fun getScreenSizeCategory(metrics: DisplayMetrics): String {
        val screenWidthDp = metrics.widthPixels / metrics.density
        val screenHeightDp = metrics.heightPixels / metrics.density

        return when {
            screenWidthDp < 600 -> "手机"
            screenWidthDp < 900 -> "小平板"
            else -> "大平板"
        }
    }

    /**
     * 查找并分析EQ控制组件
     */
    private fun findAndAnalyzeEQComponents(rootNode: AccessibilityNodeInfo) {
        findEQComponentsRecursive(rootNode, 0, 4)
    }

    /**
     * 递归查找EQ控制组件
     */
    private fun findEQComponentsRecursive(node: AccessibilityNodeInfo, depth: Int, maxDepth: Int) {
        if (depth > maxDepth) return

        try {
            val className = node.className?.toString() ?: ""
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            // 查找可能的EQ控制组件
            if (className.contains("equalizer") ||
                className.contains("b0") ||
                (className.contains("View") && bounds.width() in 100..300 && bounds.height() > 400)) {

                Log.d(TAG, "EQ控制组件: $className")
                Log.d(TAG, "  边界: $bounds")
                Log.d(TAG, "  可点击: ${node.isClickable}, 可滚动: ${node.isScrollable}")
                Log.d(TAG, "  可聚焦: ${node.isFocusable}, 已启用: ${node.isEnabled}")
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    findEQComponentsRecursive(child, depth + 1, maxDepth)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "分析EQ控制组件时出错", e)
        }
    }

    /**
     * 分析当前EQ设置
     */
    private fun analyzeCurrentEQSettings(rootNode: AccessibilityNodeInfo) {
        // 尝试通过各种方法获取当前EQ设置
        Log.d(TAG, "尝试分析当前EQ设置...")

        // 方法1: 查找可能显示当前值的文本节点
        val possibleValues = arrayOf("-6", "-5", "-4", "-3", "-2", "-1", "0", "+1", "+2", "+3", "+4", "+5", "+6")
        for (value in possibleValues) {
            val nodes = findAllNodesByText(rootNode, value)
            if (nodes.isNotEmpty()) {
                Log.d(TAG, "找到可能的EQ值 '$value': ${nodes.size} 个节点")
                for (i in nodes.indices) {
                    val bounds = Rect()
                    nodes[i].getBoundsInScreen(bounds)
                    Log.d(TAG, "  节点${i+1}: 中心(${bounds.centerX()}, ${bounds.centerY()})")
                }
            }
        }

        // 方法2: 查找可能的滑块或控制点
        findSliderControls(rootNode)
    }

    /**
     * 查找所有匹配文本的节点
     */
    private fun findAllNodesByText(rootNode: AccessibilityNodeInfo, text: String): List<AccessibilityNodeInfo> {
        val results = mutableListOf<AccessibilityNodeInfo>()
        findAllNodesByTextRecursive(rootNode, text, results)
        return results
    }

    /**
     * 递归查找所有匹配文本的节点
     */
    private fun findAllNodesByTextRecursive(node: AccessibilityNodeInfo, text: String, results: MutableList<AccessibilityNodeInfo>) {
        try {
            if (node.text?.toString()?.contains(text) == true ||
                node.contentDescription?.toString()?.contains(text) == true) {
                results.add(node)
            }

            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    findAllNodesByTextRecursive(child, text, results)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找所有匹配文本节点时出错", e)
        }
    }

    /**
     * 查找滑块控制
     */
    private fun findSliderControls(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "查找滑块控制...")
        findSliderControlsRecursive(rootNode, 0, 4)
    }

    /**
     * 递归查找滑块控制
     */
    private fun findSliderControlsRecursive(node: AccessibilityNodeInfo, depth: Int, maxDepth: Int) {
        if (depth > maxDepth) return

        try {
            val className = node.className?.toString() ?: ""
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            if (className.contains("SeekBar") ||
                className.contains("Slider") ||
                className.contains("Progress") ||
                (node.isClickable && bounds.height() > bounds.width() && bounds.height() > 200)) {

                Log.d(TAG, "可能的滑块控制: $className")
                Log.d(TAG, "  边界: $bounds")
                Log.d(TAG, "  属性: 可点击=${node.isClickable}, 可滚动=${node.isScrollable}")
            }

            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    findSliderControlsRecursive(child, depth + 1, maxDepth)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找滑块控制时出错", e)
        }
    }

    /**
     * 检测当前EQ控制点位置
     */
    private fun detectCurrentEQControlPoints(rootNode: AccessibilityNodeInfo) {
        try {
            Log.d(TAG, "尝试检测当前EQ控制点的实际位置...")

            // 根据日志信息，每个频段都有对应的EQ控制组件
            // 边界分别是：
            // 62Hz: Rect(174, 1944 - 351, 2581)
            // 250Hz: Rect(351, 1944 - 529, 2581)
            // 1kHz: Rect(529, 1944 - 707, 2581)
            // 4kHz: Rect(707, 1944 - 885, 2581)
            // 8kHz: Rect(885, 1944 - 1063, 2581)
            // 16kHz: Rect(1063, 1944 - 1241, 2581)

            val frequencies = arrayOf("62", "250", "1k", "4k", "8k", "16k")
            val expectedBounds = arrayOf(
                Rect(174, 1944, 351, 2581),   // 62Hz
                Rect(351, 1944, 529, 2581),   // 250Hz
                Rect(529, 1944, 707, 2581),   // 1kHz
                Rect(707, 1944, 885, 2581),   // 4kHz
                Rect(885, 1944, 1063, 2581),  // 8kHz
                Rect(1063, 1944, 1241, 2581)  // 16kHz
            )

            // 尝试在每个控制区域内查找可能的控制点或当前值指示器
            for (i in frequencies.indices) {
                Log.d(TAG, "检测频段 ${frequencies[i]}Hz 的控制点:")
                val controlArea = expectedBounds[i]
                Log.d(TAG, "  控制区域: $controlArea")

                // 在这个区域内查找可能的控制点
                findControlPointsInArea(rootNode, controlArea, frequencies[i])
            }

        } catch (e: Exception) {
            Log.e(TAG, "检测EQ控制点位置时出错", e)
        }
    }

    /**
     * 在指定区域内查找控制点
     */
    private fun findControlPointsInArea(rootNode: AccessibilityNodeInfo, area: Rect, frequency: String) {
        findControlPointsInAreaRecursive(rootNode, area, frequency, 0, 5)
    }

    /**
     * 递归在指定区域内查找控制点
     */
    private fun findControlPointsInAreaRecursive(node: AccessibilityNodeInfo, area: Rect, frequency: String, depth: Int, maxDepth: Int) {
        if (depth > maxDepth) return

        try {
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            // 检查节点是否在指定区域内
            if (area.contains(bounds) || Rect.intersects(area, bounds)) {
                val className = node.className?.toString() ?: ""
                val text = node.text?.toString() ?: ""
                val contentDesc = node.contentDescription?.toString() ?: ""

                // 查找可能的控制点或数值指示器
                if (text.isNotEmpty() || contentDesc.isNotEmpty() ||
                    className.contains("Button") || className.contains("Image") ||
                    (bounds.width() < 50 && bounds.height() < 50)) { // 小的可能是控制点

                    Log.d(TAG, "    可能的控制点: $className")
                    Log.d(TAG, "      位置: 中心(${bounds.centerX()}, ${bounds.centerY()})")
                    Log.d(TAG, "      文本: '$text', 描述: '$contentDesc'")
                    Log.d(TAG, "      边界: $bounds")
                }
            }

            // 递归检查子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    findControlPointsInAreaRecursive(child, area, frequency, depth + 1, maxDepth)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "在区域内查找控制点时出错", e)
        }
    }

    /**
     * 打印EQ界面结构（用于调试）
     */
    private fun printEQStructure(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "=== EQ界面分析开始 ===")
        printEQNodeDetails(rootNode, 0)
        Log.d(TAG, "=== EQ界面分析结束 ===")
    }

    /**
     * 打印EQ节点详细信息
     */
    private fun printEQNodeDetails(node: AccessibilityNodeInfo?, depth: Int = 0) {
        if (node == null) return

        val indent = "  ".repeat(depth)
        val text = node.text?.toString() ?: ""
        val contentDesc = node.contentDescription?.toString() ?: ""
        val className = node.className?.toString() ?: ""
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        Log.d(TAG, "${indent}Node: $className")
        Log.d(TAG, "${indent}  text: '$text', desc: '$contentDesc'")
        Log.d(TAG, "${indent}  bounds: $bounds, clickable: ${node.isClickable}, scrollable: ${node.isScrollable}")
        Log.d(TAG, "${indent}  enabled: ${node.isEnabled}, focusable: ${node.isFocusable}")

        for (i in 0 until node.childCount) {
            val child = node.getChild(i)
            if (child != null) {
                printEQNodeDetails(child, depth + 1)
                child.recycle()
            }
        }
    }

    /**
     * 等待EQ模态框出现
     */
    private fun waitForEQModal() {
        if (!isAutomationRunning) return

        Log.d(TAG, "等待EQ模态框出现")

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 优先检查并处理"取消"按钮
            if (checkAndHandleCancelButton(rootNode)) {
                handler.postDelayed({ waitForEQModal() }, STEP_DELAY)
                return
            }

            // 检查是否出现了EQ模态框的特征元素
            val modalIndicators = arrayOf(
                "关闭",      // 模态框的关闭按钮
                "重命名",    // 模态框的重命名按钮
                "62",        // 频段标签
                "250",       // 频段标签
                "+6 dB",     // 增益标签
                "-6 dB"      // 增益标签
            )

            var foundIndicators = 0
            for (indicator in modalIndicators) {
                if (findNodeByText(rootNode, indicator) != null) {
                    foundIndicators++
                }
            }

            if (foundIndicators >= 4) { // 至少找到4个指示器才认为模态框已完全加载
                Log.d(TAG, "EQ模态框已完全加载，找到 $foundIndicators 个指示器")
                // 额外等待一小段时间确保模态框完全稳定
                handler.postDelayed({
                    Log.d(TAG, "模态框稳定等待完成，开始EQ调节")
                    adjustEqualizer()
                }, 200) // 额外等待0.2秒确保稳定
            } else {
                Log.d(TAG, "EQ模态框还未完全加载，找到 $foundIndicators 个指示器，继续等待")
                handler.postDelayed({ waitForEQModal() }, DETECTION_INTERVAL)
            }
        } else {
            handler.postDelayed({ waitForEQModal() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 调整EQ均衡器
     * 目标值：62Hz(+3), 250Hz(+4), 1kHz(-1), 4kHz(0), 8kHz(-5), 16kHz(-6)
     */
    private fun adjustEqualizer() {
        if (!isAutomationRunning) return

        Log.d(TAG, "开始调整EQ均衡器")
        currentStep = 5

        val rootNode = rootInActiveWindow
        if (rootNode != null) {
            // 检查是否已经在EQ界面
            val eqIndicator = findNodeByText(rootNode, "Seeq") ?:
                             findNodeByText(rootNode, "62") ?:
                             findNodeByText(rootNode, "250")

            if (eqIndicator == null) {
                Log.w(TAG, "似乎不在EQ界面，重试...")
                handler.postDelayed({ adjustEqualizer() }, DETECTION_INTERVAL)
                return
            }

            Log.d(TAG, "确认在EQ界面，进行最终稳定性检查")

            // 再次确认EQ模态框完全稳定
            val modalIndicators = arrayOf("关闭", "重命名", "62", "250", "+6 dB", "-6 dB")
            var foundIndicators = 0
            for (indicator in modalIndicators) {
                if (findNodeByText(rootNode, indicator) != null) {
                    foundIndicators++
                }
            }

            if (foundIndicators < 4) {
                Log.w(TAG, "EQ模态框状态不稳定，只找到 $foundIndicators 个指示器，重新等待...")
                handler.postDelayed({ waitForEQModal() }, MODAL_WAIT_DELAY)
                return
            }

            Log.d(TAG, "EQ模态框状态稳定，找到 $foundIndicators 个指示器，开始调节")

            // 先输出详细的EQ界面布局信息
            analyzeEQLayout(rootNode)

            // 使用自定义的EQ参数 (对应62Hz, 250Hz, 1kHz, 4kHz, 8kHz, 16kHz)
            val targetGains = customEQGains
            val frequencies = arrayOf("62", "250", "1k", "4k", "8k", "16k")

            Log.d(TAG, "使用自定义EQ参数: ${targetGains.contentToString()}")
            Log.d(TAG, "对应频段: 62Hz(${targetGains[0]}dB), 250Hz(${targetGains[1]}dB), 1kHz(${targetGains[2]}dB), 4kHz(${targetGains[3]}dB), 8kHz(${targetGains[4]}dB), 16kHz(${targetGains[5]}dB)")

            // 动态获取EQ界面的坐标信息
            val eqCoordinates = getEQCoordinates(rootNode, frequencies)
            if (eqCoordinates != null) {
                Log.d(TAG, "成功获取EQ坐标信息")
                // 使用动态获取的坐标调节频段
                adjustFrequencyBandByClick(0, frequencies, targetGains, eqCoordinates)
            } else {
                Log.w(TAG, "动态获取坐标失败，尝试使用估算坐标")
                // 使用屏幕比例估算的备用方案
                val fallbackCoordinates = getFallbackEQCoordinates()
                if (fallbackCoordinates != null) {
                    Log.d(TAG, "使用备用坐标方案")
                    adjustFrequencyBandByClick(0, frequencies, targetGains, fallbackCoordinates)
                } else {
                    Log.e(TAG, "所有坐标获取方法都失败，操作终止")
                    isAutomationRunning = false
                }
            }

        } else {
            // 如果无法获取根节点，重试
            handler.postDelayed({ adjustEqualizer() }, DETECTION_INTERVAL)
        }
    }

    /**
     * 按顺序通过点击调节频段
     */
    private fun adjustFrequencyBandByClick(
        index: Int,
        frequencies: Array<String>,
        targetGains: Array<Int>,
        eqCoordinates: EQCoordinates
    ) {
        if (!isAutomationRunning || index >= frequencies.size) {
            if (index >= frequencies.size) {
                eqAdjustmentCount++
                Log.d(TAG, "EQ调节完成，第 $eqAdjustmentCount 次执行完毕")

                if (eqAdjustmentCount < MAX_EQ_ADJUSTMENTS) {
                    Log.d(TAG, "等待0.5秒后重复执行EQ调节（第 ${eqAdjustmentCount + 1} 次）")
                    handler.postDelayed({
                        Log.d(TAG, "开始重复执行EQ调节")
                        adjustEqualizer()
                    }, 500) // 等待0.5秒后重复执行
                } else {
                    Log.d(TAG, "EQ调节已执行 $eqAdjustmentCount 次，自动化操作完全结束")
                    isAutomationRunning = false
                    currentStep = 6
                }
            }
            return
        }

        val frequency = frequencies[index]
        val targetGain = targetGains[index]
        val centerX = eqCoordinates.bandCenterX[index]

        Log.d(TAG, "调节频段 ${frequency}Hz 到 ${targetGain}dB")

        // 使用纯粹的线性映射，基于增益标签的实际位置
        // EQ范围：+6dB(顶部) 到 -6dB(底部)，总共12dB范围
        val normalizedGain = (6f - targetGain) / 12f // 将+6到-6转换为0到1
        val targetY = eqCoordinates.eqTopY + (normalizedGain * eqCoordinates.eqHeight)

        Log.d(TAG, "点击坐标: ($centerX, $targetY) - 频段${frequency}Hz设置为${targetGain}dB")

        // 直接点击目标位置
        performClickGesture(centerX, targetY)

        // 高速模式：快速调节下一个频段
        handler.postDelayed({
            adjustFrequencyBandByClick(index + 1, frequencies, targetGains, eqCoordinates)
        }, CLICK_DELAY) // 高速模式：0.05秒间隔
    }









    /**
     * 动态获取EQ界面的坐标信息（自适应不同分辨率）
     */
    private fun getEQCoordinates(rootNode: AccessibilityNodeInfo, frequencies: Array<String>): EQCoordinates? {
        try {
            Log.d(TAG, "开始动态获取EQ坐标信息")

            // 1. 查找频段标签节点，获取X坐标
            val bandCenterX = FloatArray(6)
            var foundFrequencies = 0

            for (i in frequencies.indices) {
                val frequencyNode = findNodeByText(rootNode, frequencies[i])
                if (frequencyNode != null) {
                    val bounds = Rect()
                    frequencyNode.getBoundsInScreen(bounds)
                    bandCenterX[i] = bounds.centerX().toFloat()
                    foundFrequencies++
                    Log.d(TAG, "找到频段 ${frequencies[i]}: X坐标 = ${bandCenterX[i]}")
                } else {
                    Log.w(TAG, "未找到频段标签: ${frequencies[i]}")
                }
            }

            if (foundFrequencies < 6) {
                Log.e(TAG, "只找到 $foundFrequencies 个频段标签，需要6个")
                return null
            }

            // 2. 通过增益标签动态计算EQ控制区域的Y坐标范围
            val gainTopNode = findNodeByText(rootNode, "+6 dB")
            val gainBottomNode = findNodeByText(rootNode, "-6 dB")

            if (gainTopNode == null || gainBottomNode == null) {
                Log.e(TAG, "未找到增益标签，无法确定EQ控制区域")
                return null
            }

            val topBounds = Rect()
            val bottomBounds = Rect()
            gainTopNode.getBoundsInScreen(topBounds)
            gainBottomNode.getBoundsInScreen(bottomBounds)

            val topLabelY = topBounds.centerY().toFloat()
            val bottomLabelY = bottomBounds.centerY().toFloat()

            Log.d(TAG, "+6dB标签Y坐标: $topLabelY, -6dB标签Y坐标: $bottomLabelY")

            // 重新分析测试结果，找到根本问题：
            // 测试结果显示：
            // - 0dB点击Y=2262.5，结果正确 ✓
            // - -1dB点击Y=2315.6，结果正确 ✓
            // - 但-6dB点击Y=2581（EQ控制区域最底部），却显示为-1dB
            //
            // 这说明：实际的EQ控制范围并不是1944-2581！
            // 真正的控制范围应该是基于增益标签位置的

            // 从测试结果反推真实的控制范围：
            // - +6dB标签位置: Y=2032
            // - 0dB标签位置: Y=2262 (测试验证准确)
            // - -6dB标签位置: Y=2492
            //
            // 这些标签位置很可能就是实际的控制点位置！
            val eqTopY = topLabelY      // +6dB实际控制位置: 2032
            val eqCenterY = 2262f       // 0dB实际控制位置: 2262 (测试验证)
            val eqBottomY = bottomLabelY // -6dB实际控制位置: 2492
            val eqHeight = eqBottomY - eqTopY // 实际控制高度: 460像素

            Log.d(TAG, "使用精确的EQ控制区域坐标:")
            Log.d(TAG, "  +6dB标签位置: Y=$topLabelY")
            Log.d(TAG, "  -6dB标签位置: Y=$bottomLabelY")
            Log.d(TAG, "  EQ控制区域: Y=$eqTopY 到 Y=$eqBottomY")
            Log.d(TAG, "  控制区域高度: $eqHeight")

            // 显示基于增益标签位置的新坐标计算
            Log.d(TAG, "--- 基于增益标签位置的EQ坐标计算 ---")
            Log.d(TAG, "控制范围重新定义:")
            Log.d(TAG, "  +6dB控制位置: Y=$eqTopY (增益标签位置)")
            Log.d(TAG, "  0dB控制位置: Y=$eqCenterY (测试验证准确)")
            Log.d(TAG, "  -6dB控制位置: Y=$eqBottomY (增益标签位置)")
            Log.d(TAG, "  实际控制高度: $eqHeight 像素")

            // 计算目标值对应的新Y坐标
            Log.d(TAG, "--- 目标值对应的新Y坐标 ---")
            val targetValues = arrayOf(3, 4, -1, 0, -5, -6)
            val frequencies = arrayOf("62Hz", "250Hz", "1kHz", "4kHz", "8kHz", "16kHz")
            for (i in targetValues.indices) {
                val targetGain = targetValues[i]
                val normalizedGain = (6f - targetGain) / 12f
                val newY = eqTopY + (normalizedGain * eqHeight)
                Log.d(TAG, "  ${frequencies[i]}(${targetGain}dB): 新Y坐标 = $newY")
            }

            // 验证关键点
            Log.d(TAG, "--- 关键点验证 ---")
            Log.d(TAG, "  +6dB: Y=${eqTopY} (应该在增益标签位置)")
            Log.d(TAG, "  0dB: Y=${eqTopY + (6f/12f * eqHeight)} (应该接近$eqCenterY)")
            Log.d(TAG, "  -6dB: Y=${eqBottomY} (应该在增益标签位置)")

            // 设备兼容性信息
            Log.d(TAG, "--- 设备兼容性信息 ---")
            val density = resources.displayMetrics.density
            val densityDpi = resources.displayMetrics.densityDpi
            Log.d(TAG, "  屏幕密度: ${density}x (${densityDpi} DPI)")
            Log.d(TAG, "  控制区域高度: $eqHeight 像素 (${eqHeight/density} dp)")
            Log.d(TAG, "  增益标签间距: ${eqBottomY - eqTopY} 像素")

            // 布局合理性检查
            if (eqHeight < 200 || eqHeight > 1000) {
                Log.w(TAG, "警告: EQ控制区域高度异常 ($eqHeight px)，可能存在兼容性问题")
            }
            if (Math.abs((eqTopY + 6f/12f * eqHeight) - eqCenterY) > 50) {
                Log.w(TAG, "警告: 0dB位置计算偏差较大，可能存在布局差异")
            }

            Log.d(TAG, "EQ控制区域（动态计算）: 顶部=$eqTopY, 底部=$eqBottomY, 高度=$eqHeight")

            // 3. 查找增益标签来验证Y坐标范围
            val gainLabels = arrayOf("+6 dB", "0 dB", "-6 dB")
            for (label in gainLabels) {
                val gainNode = findNodeByText(rootNode, label)
                if (gainNode != null) {
                    val bounds = Rect()
                    gainNode.getBoundsInScreen(bounds)
                    Log.d(TAG, "找到增益标签 '$label': Y坐标 = ${bounds.centerY()}")
                }
            }

            return EQCoordinates(
                bandCenterX = bandCenterX.toTypedArray(),
                eqTopY = eqTopY,
                eqBottomY = eqBottomY,
                eqHeight = eqHeight
            )

        } catch (e: Exception) {
            Log.e(TAG, "获取EQ坐标信息时出错", e)
            return null
        }
    }

    /**
     * 查找EQ模态框
     */
    private fun findEQModal(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        return findEQModalRecursive(rootNode)
    }

    /**
     * 递归查找EQ模态框
     */
    private fun findEQModalRecursive(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            val className = node.className?.toString() ?: ""
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            // 查找模态框特征：
            // 1. 通常是FrameLayout或类似的容器
            // 2. 位于屏幕下半部分
            // 3. 包含"关闭"、"重命名"等按钮
            // 4. 包含频段标签
            if ((className.contains("Layout") || className.contains("Dialog")) &&
                bounds.top > resources.displayMetrics.heightPixels * 0.3f && // 在屏幕下半部分
                bounds.height() > 300) { // 有一定高度

                // 检查是否包含模态框特征元素
                if (containsModalElements(node)) {
                    Log.d(TAG, "找到EQ模态框: $className, bounds: $bounds")
                    return node
                }
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    val result = findEQModalRecursive(child)
                    if (result != null) {
                        return result
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找EQ模态框时出错", e)
        }

        return null
    }

    /**
     * 检查节点是否包含模态框元素
     */
    private fun containsModalElements(node: AccessibilityNodeInfo): Boolean {
        val modalElements = arrayOf("关闭", "重命名", "62", "250", "+6 dB", "-6 dB")
        var foundElements = 0

        for (element in modalElements) {
            if (findNodeByTextInSubtree(node, element) != null) {
                foundElements++
            }
        }

        return foundElements >= 3 // 至少找到3个元素才认为是模态框
    }

    /**
     * 在子树中查找文本节点
     */
    private fun findNodeByTextInSubtree(rootNode: AccessibilityNodeInfo, text: String): AccessibilityNodeInfo? {
        return findNodeByTextInSubtreeRecursive(rootNode, text, 0, 5) // 限制递归深度
    }

    /**
     * 递归在子树中查找文本节点
     */
    private fun findNodeByTextInSubtreeRecursive(node: AccessibilityNodeInfo, text: String, depth: Int, maxDepth: Int): AccessibilityNodeInfo? {
        if (depth > maxDepth) return null

        try {
            // 检查当前节点
            if (node.text?.toString()?.contains(text, ignoreCase = true) == true ||
                node.contentDescription?.toString()?.contains(text, ignoreCase = true) == true) {
                return node
            }

            // 递归检查子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    val result = findNodeByTextInSubtreeRecursive(child, text, depth + 1, maxDepth)
                    if (result != null) {
                        return result
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "在子树中查找文本节点时出错", e)
        }

        return null
    }

    /**
     * 查找EQ控制区域
     */
    private fun findEQControlArea(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 首先尝试查找模态框
        val modal = findEQModal(rootNode)
        if (modal != null) {
            Log.d(TAG, "在模态框中查找EQ控制区域")
            return findEQControlAreaInModal(modal)
        }

        // 如果没找到模态框，使用原来的方法
        return findEQControlAreaRecursive(rootNode)
    }

    /**
     * 在模态框中查找EQ控制区域
     */
    private fun findEQControlAreaInModal(modalNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        return findEQControlAreaRecursive(modalNode)
    }

    /**
     * 递归查找EQ控制区域
     */
    private fun findEQControlAreaRecursive(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        try {
            val className = node.className?.toString() ?: ""
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            // 查找包含EQ控制组件的区域
            // 通常是一个较大的View，包含多个子组件
            if ((className.contains("View") || className.contains("Layout")) &&
                bounds.width() > 300 && bounds.height() > 400) {

                // 检查是否包含EQ相关的子节点
                if (containsEQComponents(node)) {
                    Log.d(TAG, "找到EQ控制区域: $className, bounds: $bounds")
                    return node
                }
            }

            // 递归查找子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    val result = findEQControlAreaRecursive(child)
                    if (result != null) {
                        return result
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "查找EQ控制区域时出错", e)
        }

        return null
    }

    /**
     * 检查节点是否包含EQ组件
     */
    private fun containsEQComponents(node: AccessibilityNodeInfo): Boolean {
        return containsEQComponentsRecursive(node, 0, 3) // 限制递归深度为3
    }

    /**
     * 递归检查是否包含EQ组件
     */
    private fun containsEQComponentsRecursive(node: AccessibilityNodeInfo, depth: Int, maxDepth: Int): Boolean {
        if (depth > maxDepth) return false

        try {
            val className = node.className?.toString() ?: ""

            // 查找EQ相关的组件类名
            if (className.contains("equalizer") ||
                className.contains("b0") || // 根据调试信息中的com.oplus.melody.ui.component.detail.equalizer.b0
                className.contains("Equalizer")) {
                return true
            }

            // 递归检查子节点
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    if (containsEQComponentsRecursive(child, depth + 1, maxDepth)) {
                        return true
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查EQ组件时出错", e)
        }

        return false
    }

    /**
     * 获取智能备用EQ坐标（基于已验证的比例关系）
     */
    private fun getFallbackEQCoordinates(): EQCoordinates? {
        try {
            val screenWidth = resources.displayMetrics.widthPixels
            val screenHeight = resources.displayMetrics.heightPixels
            val density = resources.displayMetrics.density

            Log.d(TAG, "智能备用坐标计算:")
            Log.d(TAG, "  屏幕尺寸: ${screenWidth}x${screenHeight}")
            Log.d(TAG, "  屏幕密度: ${density}x")

            // 基于已验证的设备数据建立比例关系
            // 参考设备: 1280x2772, 增益标签位置: +6dB(Y=2032), -6dB(Y=2492)
            val referenceWidth = 1280f
            val referenceHeight = 2772f
            val referencePlus6Y = 2032f
            val referenceMinus6Y = 2492f

            // 计算缩放比例
            val scaleX = screenWidth / referenceWidth
            val scaleY = screenHeight / referenceHeight

            // 使用Y轴缩放来计算EQ控制区域（因为EQ主要是垂直控制）
            val eqControlTop = referencePlus6Y * scaleY
            val eqControlBottom = referenceMinus6Y * scaleY
            val eqControlHeight = eqControlBottom - eqControlTop

            Log.d(TAG, "  缩放比例: X=${scaleX}, Y=${scaleY}")
            Log.d(TAG, "  EQ控制区域: 顶部=$eqControlTop, 底部=$eqControlBottom")
            Log.d(TAG, "  控制高度: $eqControlHeight 像素 (${eqControlHeight/density} dp)")

            // 基于已验证的频段X坐标比例计算
            // 参考坐标: 262, 440, 618, 796, 974, 1152 (在1280宽度屏幕上)
            val referenceX = arrayOf(262f, 440f, 618f, 796f, 974f, 1152f)
            val bandCenterX = FloatArray(6)

            for (i in referenceX.indices) {
                bandCenterX[i] = referenceX[i] * scaleX
            }

            Log.d(TAG, "  频段X坐标: ${bandCenterX.contentToString()}")

            // 合理性检查
            if (eqControlHeight < 100 * density || eqControlHeight > 500 * density) {
                Log.w(TAG, "警告: 备用坐标的EQ控制高度可能不合理 ($eqControlHeight px)")
            }

            return EQCoordinates(
                bandCenterX = bandCenterX.toTypedArray(),
                eqTopY = eqControlTop,
                eqBottomY = eqControlBottom,
                eqHeight = eqControlHeight
            )

        } catch (e: Exception) {
            Log.e(TAG, "获取备用EQ坐标时出错", e)
            return null
        }
    }

    /**
     * 执行点击手势（用于EQ调节）
     */
    private fun performClickGesture(x: Float, y: Float) {
        try {
            val path = Path()
            path.moveTo(x, y)

            val gestureBuilder = GestureDescription.Builder()
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 20) // 高速模式：更短的点击时间
            gestureBuilder.addStroke(strokeDescription)

            val gesture = gestureBuilder.build()
            dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Log.d(TAG, "EQ点击完成: ($x, $y)")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "EQ点击被取消: ($x, $y)")
                }
            }, null)
        } catch (e: Exception) {
            Log.e(TAG, "执行EQ点击失败: ($x, $y)", e)
        }
    }
}
